import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsUUID,
  IsObject,
  IsOptional,
  IsNumber,
  IsArray,
  IsEnum,
  ValidateNested,
  Min,
  Max,
} from 'class-validator';
import { QueryDto } from '@common/dto';

/**
 * Enum cho loại memory
 */
export enum MemoryTypeEnum {
  SKILL = 'skill',
  KNOWLEDGE = 'knowledge',
  PERSONALITY = 'personality',
  EXPERIENCE = 'experience',
  OTHER = 'other',
}

/**
 * Enum cho trạng thái memory
 */
export enum MemoryStatusEnum {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DEPRECATED = 'deprecated',
}

/**
 * DTO cho structured content của memory
 */
export class StructuredContentDto {
  @ApiPropertyOptional({
    description: 'Tên kỹ năng hoặc kiến thức',
    example: 'JavaScript Programming',
  })
  @IsOptional()
  @IsString()
  skill_name?: string;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON> tả chi tiết',
    example: '<PERSON><PERSON><PERSON> thức về lập trình JavaScript cơ bản và nâng cao',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Các ví dụ minh họa',
    example: ['const x = 10;', 'function hello() { return "Hello World"; }'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  examples?: string[];

  @ApiPropertyOptional({
    description: 'Loại kiến thức',
    enum: MemoryTypeEnum,
    example: MemoryTypeEnum.SKILL,
  })
  @IsOptional()
  @IsEnum(MemoryTypeEnum)
  type?: MemoryTypeEnum;

  @ApiPropertyOptional({
    description: 'Mức độ quan trọng (1-10)',
    example: 8,
    minimum: 1,
    maximum: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  importance?: number;

  @ApiPropertyOptional({
    description: 'Các từ khóa liên quan',
    example: ['javascript', 'programming', 'web development'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[];

  @ApiPropertyOptional({
    description: 'Ngữ cảnh sử dụng',
    example: 'Sử dụng khi cần hỗ trợ lập trình JavaScript',
  })
  @IsOptional()
  @IsString()
  context?: string;
}

/**
 * DTO cho metadata của memory
 */
export class MetadataDto {
  @ApiPropertyOptional({
    description: 'Nguồn gốc của kiến thức',
    example: 'Training data',
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({
    description: 'Độ tin cậy (0-1)',
    example: 0.95,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence?: number;

  @ApiPropertyOptional({
    description: 'Ngày cập nhật cuối (timestamp)',
    example: 1703123456789,
  })
  @IsOptional()
  @IsNumber()
  last_updated?: number;

  @ApiPropertyOptional({
    description: 'Số lần sử dụng',
    example: 25,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  usage_count?: number;

  @ApiPropertyOptional({
    description: 'Thẻ phân loại',
    example: ['programming', 'javascript', 'frontend'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Ghi chú bổ sung',
    example: 'Memory này được cập nhật từ phiên bản mới nhất',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái memory',
    enum: MemoryStatusEnum,
    example: MemoryStatusEnum.ACTIVE,
  })
  @IsOptional()
  @IsEnum(MemoryStatusEnum)
  status?: MemoryStatusEnum;
}

/**
 * DTO để tạo memory mới
 */
export class CreateAgentMemoryDto {
  @ApiProperty({
    description: 'UUID của agent',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  agentId: string;

  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: StructuredContentDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => StructuredContentDto)
  structuredContent: StructuredContentDto;

  @ApiPropertyOptional({
    description: 'Thông tin metadata bổ sung',
    type: MetadataDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => MetadataDto)
  metadata?: MetadataDto;
}

/**
 * DTO để cập nhật memory
 */
export class UpdateAgentMemoryDto {
  @ApiPropertyOptional({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: StructuredContentDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => StructuredContentDto)
  structuredContent?: StructuredContentDto;

  @ApiPropertyOptional({
    description: 'Thông tin metadata bổ sung',
    type: MetadataDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => MetadataDto)
  metadata?: MetadataDto;
}

/**
 * DTO để query memories
 */
export class AgentMemoriesQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'UUID của agent để lọc memories',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  agentId?: string;

  @ApiPropertyOptional({
    description: 'Loại memory để lọc',
    enum: MemoryTypeEnum,
    example: MemoryTypeEnum.SKILL,
  })
  @IsOptional()
  @IsEnum(MemoryTypeEnum)
  type?: MemoryTypeEnum;

  @ApiPropertyOptional({
    description: 'Mức độ quan trọng tối thiểu',
    example: 5,
    minimum: 1,
    maximum: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  minImportance?: number;

  @ApiPropertyOptional({
    description: 'Tags để lọc (cách nhau bởi dấu phẩy)',
    example: 'javascript,programming',
  })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái memory',
    enum: MemoryStatusEnum,
    example: MemoryStatusEnum.ACTIVE,
  })
  @IsOptional()
  @IsEnum(MemoryStatusEnum)
  status?: MemoryStatusEnum;
}

/**
 * DTO response cho memory
 */
export class AgentMemoryResponseDto {
  @ApiProperty({
    description: 'UUID của memory',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'UUID của agent',
    example: '123e4567-e89b-12d3-a456-************',
  })
  agentId: string;

  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: StructuredContentDto,
  })
  structuredContent: StructuredContentDto;

  @ApiPropertyOptional({
    description: 'Thông tin metadata bổ sung',
    type: MetadataDto,
  })
  metadata?: MetadataDto;

  @ApiPropertyOptional({
    description: 'Thời gian tạo (timestamp)',
    example: 1703123456789,
  })
  createdAt?: number;
}
