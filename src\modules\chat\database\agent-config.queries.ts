import { Injectable } from '@nestjs/common';
import { ChatDatabaseService } from './database.service';
import { 
  SystemAgentConfig, 
  SystemAgentConfigMap, 
  ModelProviderEnum,
  McpSseConfig 
} from '../interfaces/system-agent-config.interface';
import { AppException } from '@/common';
import { CHAT_ERROR_CODES } from '@modules/chat/exceptions';

/**
 * Interface for raw agent data from database
 */
interface RawAgentData {
  // agents table
  agent_id: string;
  agent_name: string;
  agent_instruction: string;
  agent_model_config: any; // JSONB
  agent_vector_store_id: string;
  
  // agents_system table
  system_name_code: string;
  system_description: string;
  system_is_supervisor: boolean;
  system_active: boolean;
  
  // system_models table
  model_id: string;
  model_provider: string;
  
  // system_key_llm table (aggregated)
  api_keys: string[]; // Array of API keys
  
  // mcp_systems table (aggregated)
  mcp_server_names: string[]; // Array of MCP server names
  mcp_configs: any[]; // Array of MCP configs
}

/**
 * Raw SQL queries for agent configuration data aggregation (Chat Module)
 * 
 * This service handles complex SQL joins across multiple tables to build
 * SystemAgentConfig payloads for multi-agent processing.
 */
@Injectable()
export class AgentConfigQueries {
  constructor(private readonly databaseService: ChatDatabaseService) {}

  /**
   * Get all active system agents with their complete configuration
   * @returns Promise<SystemAgentConfigMap> Map of agent configurations
   */
  async getAllSystemAgentConfigs(): Promise<SystemAgentConfigMap> {
    const query = `SELECT
                     -- agents table
                     a.id as agent_id,
                     a.name as agent_name,
                     a.instruction as agent_instruction,
                     a.model_config as agent_model_config,
                     a.vector_store_id as agent_vector_store_id,

                     -- agents_system table
                     ags.name_code as system_name_code,
                     ags.description as system_description,
                     ags.is_supervisor as system_is_supervisor,
                     ags.active as system_active,

                     -- system_models table
                     sm.model_id,
                     sm.provider as model_provider,
                     sm.is_fine_tuned as model_is_fine_tuned,

                     -- model registry table

                     mr.base_pricing as model_base_pricing,
                     mr.fine_tune_pricing as model_fine_tune_pricing,

                     -- Aggregated API keys
                     COALESCE(
                         array_agg(DISTINCT skl.api_key) FILTER (WHERE skl.api_key IS NOT NULL),
                         ARRAY[]::text[]
                     ) as api_keys,

                     -- Aggregated MCP server names
                     COALESCE(
                         array_agg(DISTINCT ms.name_server) FILTER (WHERE ms.name_server IS NOT NULL),
                         ARRAY[]::text[]
                     ) as mcp_server_names,

                     -- Aggregated MCP configs
                     COALESCE(
                         array_agg(DISTINCT ms.config) FILTER (WHERE ms.config IS NOT NULL),
                         ARRAY[]::jsonb[]
                     ) as mcp_configs

                   FROM agents a
                          INNER JOIN agents_system ags ON a.id = ags.id
                          INNER JOIN system_models sm ON ags.system_model_id = sm.id
                          INNER JOIN model_registry mr ON sm.model_registry_id = mr.id AND mr.deleted_at IS NULL
                          LEFT JOIN system_model_key_llm smkl ON sm.id = smkl.model_id
                          LEFT JOIN system_key_llm skl ON smkl.llm_key_id = skl.id AND skl.deleted_at IS NULL
                          LEFT JOIN agent_system_mcp asm ON ags.id = asm.agent_id
                          LEFT JOIN mcp_systems ms ON asm.mcp_id = ms.id

                   WHERE ags.active = true
                     AND a.deleted_at IS NULL
                     AND sm.active = true

                   GROUP BY
                     a.id, a.name, a.instruction, a.model_config, a.vector_store_id,
                     ags.name_code, ags.description, ags.is_supervisor, ags.active,
                     sm.model_id, sm.provider,  mr.base_pricing, mr.fine_tune_pricing, is_fine_tuned

                   ORDER BY ags.is_supervisor DESC, a.name`;

    const rawResults = await this.databaseService.query(query);
    return this.buildSystemAgentConfigMap(rawResults);
  }

  /**
   * Get system agent configuration by agent ID
   * @param agentId Agent ID
   * @returns Promise<SystemAgentConfig | null>
   */
  async getSystemAgentConfigById(agentId: string): Promise<SystemAgentConfig | null> {
    const query = `
      SELECT 
        -- agents table
        a.id as agent_id,
        a.name as agent_name,
        a.instruction as agent_instruction,
        a.model_config as agent_model_config,
        a.vector_store_id as agent_vector_store_id,
        
        -- agents_system table
        ags.name_code as system_name_code,
        ags.description as system_description,
        ags.is_supervisor as system_is_supervisor,
        ags.active as system_active,
        
        -- system_models table
        sm.model_id,
        sm.provider as model_provider,
        
        -- Aggregated API keys
        COALESCE(
          array_agg(DISTINCT skl.api_key) FILTER (WHERE skl.api_key IS NOT NULL), 
          ARRAY[]::text[]
        ) as api_keys,
        
        -- Aggregated MCP server names
        COALESCE(
          array_agg(DISTINCT ms.name_server) FILTER (WHERE ms.name_server IS NOT NULL), 
          ARRAY[]::text[]
        ) as mcp_server_names,
        
        -- Aggregated MCP configs
        COALESCE(
          array_agg(DISTINCT ms.config) FILTER (WHERE ms.config IS NOT NULL), 
          ARRAY[]::jsonb[]
        ) as mcp_configs
        
      FROM agents a
      INNER JOIN agents_system ags ON a.id = ags.id
      INNER JOIN system_models sm ON ags.system_model_id = sm.id
      LEFT JOIN system_model_key_llm smkl ON sm.id = smkl.model_id
      LEFT JOIN system_key_llm skl ON smkl.llm_key_id = skl.id AND skl.deleted_at IS NULL
      LEFT JOIN agent_system_mcp asm ON ags.id = asm.agent_id
      LEFT JOIN mcp_systems ms ON asm.mcp_id = ms.id
      
      WHERE a.id = $1 
        AND ags.active = true 
        AND a.deleted_at IS NULL
        AND sm.active = true
      
      GROUP BY 
        a.id, a.name, a.instruction, a.model_config, a.vector_store_id,
        ags.name_code, ags.description, ags.is_supervisor, ags.active,
        sm.model_id, sm.provider
    `;

    const rawResults = await this.databaseService.query(query, [agentId]);
    
    if (rawResults.length === 0) {
      return null;
    }

    const configMap = this.buildSystemAgentConfigMap(rawResults);
    return configMap[agentId] || null;
  }

  /**
   * Build SystemAgentConfigMap from raw database results
   * @param rawResults Raw query results
   * @returns SystemAgentConfigMap
   */
  private buildSystemAgentConfigMap(rawResults: any[]): SystemAgentConfigMap {
    const configMap: SystemAgentConfigMap = {};

    for (const row of rawResults) {
      const agentId = row.agent_id;
      
      // Build MCP configurations
      const mcpConfig: McpSseConfig[] = [];
      if (row.mcp_server_names && row.mcp_configs) {
        for (let i = 0; i < row.mcp_server_names.length; i++) {
          if (row.mcp_server_names[i] && row.mcp_configs[i]) {
            mcpConfig.push({
              serverName: row.mcp_server_names[i],
              config: row.mcp_configs[i]
            });
          }
        }
      }

      // Parse model config from JSONB
      const modelConfig = row.agent_model_config || {};
      
      // Build SystemAgentConfig
      configMap[agentId] = {
        id: agentId,
        name: row.agent_name,
        description: row.system_description || '',
        instruction: row.agent_instruction || '',
        mcpConfig,
        vectorStoreId: row.agent_vector_store_id || '',
        isSupervisor: row.system_is_supervisor || false,
        trimmingConfig: {
          type: 'token', // Token-based trimming for optimal cost and reliability
          threshold: modelConfig.trimmingThreshold || 1000 // Token limit instead of message count
        },
        model: {
          name: row.model_id,
          provider: this.mapProviderEnum(row.model_provider),
          inputModalities: modelConfig.inputModalities || ['text'],
          outputModalities: modelConfig.outputModalities || ['text'],
          samplingParameters: modelConfig.samplingParameters || ['temperature', 'max_tokens'],
          features: modelConfig.features || ['tool_call'],
          pricing: row.model_is_fine_tuned ? row.model_fine_tune_pricing : row.model_base_pricing,
          parameters: {
            temperature: modelConfig.temperature,
            topP: modelConfig.topP,
            topK: modelConfig.topK,
            maxTokens: modelConfig.maxTokens,
            maxOutputTokens: modelConfig.maxOutputTokens
          },
          type: 'SYSTEM',
          apiKeys: row.api_keys || []
        }
      };
    }

    return configMap;
  }

  /**
   * Map database provider string to ModelProviderEnum
   * @param provider Provider string from database
   * @returns ModelProviderEnum
   */
  private mapProviderEnum(provider: string): ModelProviderEnum {
    switch (provider?.toUpperCase()) {
      case 'OPENAI':
        return ModelProviderEnum.OPENAI;
      case 'XAI':
        return ModelProviderEnum.XAI;
      case 'ANTHROPIC':
        return ModelProviderEnum.ANTHROPIC;
      case 'GOOGLE':
        return ModelProviderEnum.GOOGLE;
      case 'DEEPSEEK':
        return ModelProviderEnum.DEEPSEEK;
      default:
        throw new AppException(CHAT_ERROR_CODES.INVALID_PROVIDER); // Default fallback
    }
  }
}
