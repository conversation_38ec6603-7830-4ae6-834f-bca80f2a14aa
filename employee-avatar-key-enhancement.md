# Employee Avatar Key Enhancement

## Tổng quan thay đổi

### 🔧 **Vấn đề trước đây:**
- API `createEmployee` tạo `avatarKey` và URL tạm thời để upload avatar
- Nhưng **không lưu `avatarKey` vào entity Employee**
- Dẫn đến việc mất thông tin avatar key sau khi tạo nhân viên

### ✅ **Giải pháp đã thực hiện:**
- **Lưu `avatarKey` vào entity Employee** ngay sau khi tạo key
- **Thêm `avatar` vào response** để frontend biết key đã được lưu
- **Thêm error handling** cho việc lưu avatar key

---

## Code Changes

### Trước khi fix:
```typescript
// Nếu có yêu cầu tạo URL tạm thời để upload avatar
if (createEmployeeDto.avatarImageType && createEmployeeDto.avatarMaxSize) {
  // Tạo key cho avatar trên S3
  const avatarKey = generateS3Key({
    baseFolder: employee.id.toString(),
    categoryFolder: CategoryFolderEnum.EMPLOYEE_AVATAR_FOLDER,
  });

  // Tạo URL tạm thời để tải lên avatar
  const uploadUrl = await this.s3Service.createPresignedWithID(
    avatarKey,
    TimeIntervalEnum.FIVE_MINUTES,
    createEmployeeDto.avatarImageType,
    createEmployeeDto.avatarMaxSize,
  );

  // ❌ KHÔNG LƯU avatarKey vào entity

  return {
    ...result,
    avatarUploadUrl: uploadUrl,
    avatarKey,
    avatarUrlExpiresAt: expiresAt,
  };
}
```

### Sau khi fix:
```typescript
// Nếu có yêu cầu tạo URL tạm thời để upload avatar
if (createEmployeeDto.avatarImageType && createEmployeeDto.avatarMaxSize) {
  // Tạo key cho avatar trên S3
  const avatarKey = generateS3Key({
    baseFolder: employee.id.toString(),
    categoryFolder: CategoryFolderEnum.EMPLOYEE_AVATAR_FOLDER,
  });

  // ✅ LƯU avatarKey vào entity Employee
  try {
    await this.employeeRepository.updateAvatar(employee.id, avatarKey);
    this.logger.log(`Đã lưu avatar key ${avatarKey} cho nhân viên ${employee.id}`);
  } catch (error) {
    this.logger.error(`Lỗi khi lưu avatar key cho nhân viên ${employee.id}: ${error.message}`, error.stack);
    throw new AppException(
      EMPLOYEE_ERROR_CODES.AVATAR_UPDATE_FAILED,
      'Lưu thông tin avatar thất bại'
    );
  }

  // Tạo URL tạm thời để tải lên avatar
  const uploadUrl = await this.s3Service.createPresignedWithID(
    avatarKey,
    TimeIntervalEnum.FIVE_MINUTES,
    createEmployeeDto.avatarImageType,
    createEmployeeDto.avatarMaxSize,
  );

  return {
    ...result,
    avatar: avatarKey, // ✅ THÊM avatar vào response
    avatarUploadUrl: uploadUrl,
    avatarKey,
    avatarUrlExpiresAt: expiresAt,
  };
}
```

---

## API Usage

### Endpoint
```
POST /admin/employee
```

### Request với avatar
```json
{
  "fullName": "Nguyễn Văn A",
  "email": "<EMAIL>",
  "phoneNumber": "0987654321",
  "password": "Password123!",
  "address": "Hà Nội",
  "roleIds": [1, 2],
  "avatarImageType": "image/jpeg",
  "avatarMaxSize": 2097152
}
```

### Response mới (có avatar key)
```json
{
  "success": true,
  "message": "Tạo nhân viên thành công",
  "result": {
    "id": 123,
    "fullName": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "phoneNumber": "0987654321",
    "address": "Hà Nội",
    "createdAt": 1672531200,
    "updatedAt": 1672531200,
    "enable": true,
    "avatar": "123/employee-avatar/avatar-123-1672531200000-uuid.jpg",
    "avatarUploadUrl": "https://s3.amazonaws.com/bucket/presigned-url",
    "avatarKey": "123/employee-avatar/avatar-123-1672531200000-uuid.jpg",
    "avatarUrlExpiresAt": 1672531500000
  }
}
```

---

## Luồng hoạt động

### 1. Tạo nhân viên với avatar
```mermaid
sequenceDiagram
    participant C as Client
    participant API as Employee API
    participant DB as Database
    participant S3 as S3 Service

    C->>API: POST /admin/employee (với avatarImageType)
    API->>DB: Tạo employee record
    API->>API: Tạo avatarKey
    API->>DB: Lưu avatarKey vào employee.avatar
    API->>S3: Tạo presigned URL
    API->>C: Trả về employee + avatarKey + uploadUrl
```

### 2. Upload avatar
```mermaid
sequenceDiagram
    participant C as Client
    participant S3 as S3 Storage

    C->>S3: PUT file lên presigned URL
    S3->>C: Upload thành công
```

### 3. Lấy thông tin nhân viên
```mermaid
sequenceDiagram
    participant C as Client
    participant API as Employee API
    participant DB as Database
    participant CDN as CDN Service

    C->>API: GET /admin/employee/{id}
    API->>DB: Lấy employee (có avatar key)
    API->>CDN: Tạo CDN URL từ avatar key
    API->>C: Trả về employee với avatar URL
```

---

## Test Cases

### Test 1: Tạo nhân viên có avatar
```bash
curl -X POST "http://localhost:3000/admin/employee" \
  -H "Authorization: Bearer {JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "Test Employee",
    "email": "<EMAIL>",
    "phoneNumber": "0987654321",
    "password": "Password123!",
    "address": "Test Address",
    "avatarImageType": "image/jpeg",
    "avatarMaxSize": 2097152
  }'

# Expected: Response có avatar key và uploadUrl
```

### Test 2: Kiểm tra avatar key đã lưu vào DB
```bash
# Sau khi tạo nhân viên, lấy thông tin để kiểm tra
curl -X GET "http://localhost:3000/admin/employee/{employeeId}" \
  -H "Authorization: Bearer {JWT_TOKEN}"

# Expected: Response có avatar field với key đã lưu
```

### Test 3: Upload file lên S3
```bash
# Sử dụng avatarUploadUrl từ response để upload file
curl -X PUT "{avatarUploadUrl}" \
  -H "Content-Type: image/jpeg" \
  --data-binary @avatar.jpg

# Expected: 200 OK
```

---

## Database Schema

### Employee Table
```sql
-- Trường avatar đã tồn tại
avatar VARCHAR(500) NULL -- Lưu S3 key của avatar
```

### Ví dụ dữ liệu
```sql
INSERT INTO employees (
  id, full_name, email, phone_number, password, address, 
  avatar, created_at, updated_at, enable
) VALUES (
  123, 'Nguyễn Văn A', '<EMAIL>', '0987654321', 
  'hashed_password', 'Hà Nội',
  '123/employee-avatar/avatar-123-1672531200000-uuid.jpg',
  1672531200, 1672531200, true
);
```

---

## Benefits

### Trước khi fix:
- ❌ Avatar key không được lưu vào DB
- ❌ Mất thông tin avatar sau khi tạo
- ❌ Không thể hiển thị avatar trong danh sách nhân viên

### Sau khi fix:
- ✅ Avatar key được lưu ngay khi tạo nhân viên
- ✅ Có thể hiển thị avatar trong danh sách
- ✅ Dữ liệu nhất quán giữa S3 và database
- ✅ Error handling tốt hơn

---

## Error Handling

### Lỗi khi lưu avatar key
```json
{
  "success": false,
  "message": "Lưu thông tin avatar thất bại",
  "error": "AVATAR_UPDATE_FAILED"
}
```

### Log tracking
```
[EmployeeService] Đã lưu avatar key 123/employee-avatar/avatar-123-1672531200000-uuid.jpg cho nhân viên 123
```

---

## Notes

1. **Transactional**: Method `createEmployee` đã có `@Transactional()` nên nếu lưu avatar key thất bại, toàn bộ transaction sẽ rollback
2. **S3 Key Format**: `{employeeId}/employee-avatar/avatar-{employeeId}-{timestamp}-{uuid}.{ext}`
3. **CDN Integration**: Avatar key có thể được sử dụng với CDN service để tạo public URL
4. **Presigned URL**: Có thời hạn 5 phút, sau đó client cần upload file lên S3
