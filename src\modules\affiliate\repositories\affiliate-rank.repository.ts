import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { AffiliateRank } from '../entities/affiliate-rank.entity';
import { AffiliateRankQueryDto } from '../admin/dto';
import { PaginatedResult } from '@/common/response';

/**
 * Repository cho AffiliateRank
 * Extends Repository<AffiliateRank> theo Repository Standard #2
 */
@Injectable()
export class AffiliateRankRepository extends Repository<AffiliateRank> {
  constructor(dataSource: DataSource) {
    super(AffiliateRank, dataSource.createEntityManager());
  }

  /**
   * Tìm rank theo ID
   * @param id ID của rank
   * @returns Thông tin rank hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<AffiliateRank | null> {
    // Kiểm tra id có hợp lệ không
    if (id === null || id === undefined || isNaN(Number(id))) {
      return null;
    }
    return this.findOne({ where: { id: Number(id) } });
  }

  /**
   * Tìm rank dựa trên giá trị performance
   * @param performance Giá trị performance của tài khoản affiliate
   * @returns Thông tin rank phù hợp hoặc null nếu không tìm thấy
   */
  async findByPerformance(performance: number): Promise<AffiliateRank | null> {
    // Kiểm tra performance hợp lệ
    if (performance === null || performance === undefined || isNaN(performance)) {
      return null;
    }

    return this.createQueryBuilder('rank')
      .where('rank.minCondition <= :performance', { performance })
      .andWhere('rank.maxCondition >= :performance', { performance }) // Sửa từ > thành >= để bao gồm maxCondition
      .andWhere('rank.isActive = :isActive', { isActive: true }) // Chỉ lấy rank đang active
      .orderBy('rank.commission', 'DESC') // Nếu có nhiều rank thỏa mãn, lấy rank có commission cao nhất
      .getOne();
  }

  /**
   * Tìm danh sách rank affiliate với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách rank affiliate với phân trang
   */
  async findWithPagination(
    queryDto: AffiliateRankQueryDto,
  ): Promise<PaginatedResult<AffiliateRank>> {
    const {
      page = 1,
      limit = 10,
      search,
      isActive,
      sortBy = 'displayOrder',
      sortDirection = 'ASC',
    } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng query
    const queryBuilder = this.createQueryBuilder('rank');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere('rank.rankName LIKE :search', {
        search: `%${search}%`,
      });
    }

    // Thêm điều kiện trạng thái kích hoạt nếu có
    if (isActive !== undefined) {
      queryBuilder.andWhere('rank.isActive = :isActive', { isActive });
    }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Thêm sắp xếp và phân trang
    queryBuilder
      .orderBy(`rank.${sortBy}`, sortDirection)
      .skip(skip)
      .take(limit);

    // Lấy dữ liệu
    const items = await queryBuilder.getMany();

    // Tính toán metadata
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Tìm rank có khoảng điều kiện chồng chéo
   * @param minCondition Điều kiện tối thiểu
   * @param maxCondition Điều kiện tối đa
   * @param excludeId ID rank cần loại trừ (dùng khi cập nhật)
   * @returns Thông tin rank chồng chéo hoặc null nếu không tìm thấy
   */
  async findOverlappingRank(
    minCondition: number,
    maxCondition: number,
    excludeId?: number,
  ): Promise<AffiliateRank | null> {
    const queryBuilder = this.createQueryBuilder('rank').where(
      '((:minCondition >= rank.minCondition AND :minCondition <= rank.maxCondition) OR ' +
        '(:maxCondition >= rank.minCondition AND :maxCondition <= rank.maxCondition) OR ' +
        '(:minCondition <= rank.minCondition AND :maxCondition >= rank.maxCondition))',
      { minCondition, maxCondition },
    );

    if (excludeId) {
      queryBuilder.andWhere('rank.id != :excludeId', { excludeId });
    }

    return queryBuilder.getOne();
  }

  /**
   * Tạo rank mới
   * @param rankData Dữ liệu rank cần tạo
   * @returns Thông tin rank đã tạo
   */
  async createRank(rankData: Partial<AffiliateRank>): Promise<AffiliateRank> {
    const newRank = this.create(rankData);
    return this.save(newRank);
  }

  /**
   * Cập nhật thông tin rank
   * @param id ID của rank
   * @param rankData Dữ liệu cần cập nhật
   * @returns Kết quả cập nhật
   */
  async updateRank(
    id: number,
    rankData: Partial<AffiliateRank>,
  ): Promise<void> {
    await this.update(id, rankData);
  }

  /**
   * Cập nhật trạng thái kích hoạt của rank
   * @param id ID của rank
   * @param isActive Trạng thái kích hoạt
   * @returns Kết quả cập nhật
   */
  async updateStatus(id: number, isActive: boolean): Promise<void> {
    // Sử dụng type assertion để tránh lỗi TypeScript
    const updateData = {
      isActive,
      updatedAt: Math.floor(Date.now() / 1000),
    } as Partial<AffiliateRank>;

    await this.update(id, updateData);
  }

  /**
   * Lấy tất cả các rank
   * @returns Danh sách tất cả các rank
   */
  async find(): Promise<AffiliateRank[]> {
    return super.find({
      order: {
        displayOrder: 'ASC'
      }
    });
  }

  /**
   * Lấy khoảng minCondition và maxCondition đã sử dụng
   * @returns Mảng các khoảng điều kiện đã sử dụng
   */
  async getUsedConditionRanges(): Promise<{ minCondition: number; maxCondition: number }[]> {
    try {
      // Lấy tất cả các rank (bao gồm cả active và inactive)
      const ranks = await this.createQueryBuilder('rank')
        .select(['rank.minCondition', 'rank.maxCondition'])
        .orderBy('rank.minCondition', 'ASC')
        .getMany();

      // Nếu không có rank nào, trả về mảng rỗng
      if (!ranks || ranks.length === 0) {
        return [];
      }

      // Chuyển đổi kết quả thành mảng các khoảng điều kiện
      // Đảm bảo các giá trị là số hợp lệ
      return ranks.map(rank => {
        // Chuyển đổi giá trị null, undefined hoặc NaN thành 0
        const minCondition = rank.minCondition !== null &&
                            rank.minCondition !== undefined &&
                            !isNaN(Number(rank.minCondition)) ?
                            Number(rank.minCondition) : 0;

        const maxCondition = rank.maxCondition !== null &&
                            rank.maxCondition !== undefined &&
                            !isNaN(Number(rank.maxCondition)) ?
                            Number(rank.maxCondition) : 0;

        return {
          minCondition,
          maxCondition
        };
      });
    } catch (error) {
      // Log lỗi và ném lại ngoại lệ để service xử lý
      console.error('Error in getUsedConditionRanges:', error);
      throw error;
    }
  }

  /**
   * Xóa rank affiliate
   * @param id ID của rank cần xóa
   * @returns Kết quả xóa
   */
  async deleteRank(id: number): Promise<void> {
    await this.delete(id);
  }

  /**
   * Đếm tổng số cấp bậc affiliate
   * @returns Tổng số cấp bậc
   */
  async countTotal(): Promise<number> {
    return this.count();
  }

  /**
   * Xóa nhiều rank affiliate
   * @param ids Danh sách ID cần xóa
   * @returns Số lượng records đã được xóa
   */
  async bulkDeleteRanks(ids: number[]): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    const result = await this.createQueryBuilder()
      .delete()
      .from(AffiliateRank)
      .where('id IN (:...ids)', { ids })
      .execute();

    return result.affected || 0;
  }
}