import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho thông tin tài khoản affiliate
 */
export class AffiliateAccountInfoDto {
  @ApiProperty({
    description: 'ID của tài khoản affiliate',
    example: 123
  })
  id: number;

  @ApiProperty({
    description: 'Tên đối tác affiliate',
    example: 'Nguyễn Văn A'
  })
  partnerName: string;

  @ApiProperty({
    description: 'Loại tài khoản affiliate',
    example: 'PERSONAL'
  })
  accountType: string;

  @ApiProperty({
    description: 'Trạng thái tài khoản',
    example: 'ACTIVE'
  })
  status: string;

  @ApiProperty({
    description: 'Thời gian tạo tài kho<PERSON>n (Unix timestamp)',
    example: **********
  })
  createdAt: number;
}

/**
 * DTO cho thông tin rank affiliate
 */
export class AffiliateRankInfoDto {
  @ApiProperty({
    description: 'ID của rank',
    example: 2
  })
  id: number;

  @ApiProperty({
    description: 'Tên rank',
    example: 'Silver'
  })
  rankName: string;

  @ApiProperty({
    description: 'Icon rank',
    example: 'silver_badge.png'
  })
  rankBadge: string;

  @ApiProperty({
    description: 'Mức hoa hồng rank',
    example: 5.5
  })
  commission: number;

  @ApiProperty({
    description: 'Điều kiện tối thiểu',
    example: 10
  })
  minCondition: number;

  @ApiProperty({
    description: 'Điều kiện tối đa',
    example: 49
  })
  maxCondition: number;
}

/**
 * DTO cho thông tin tài khoản affiliate đầy đủ
 */
export class AffiliateAccountDto {
  @ApiProperty({
    description: 'Thông tin tài khoản',
    type: AffiliateAccountInfoDto
  })
  accountInfo: AffiliateAccountInfoDto;

  @ApiPropertyOptional({
    description: 'Thông tin rank',
    type: AffiliateRankInfoDto,
    nullable: true
  })
  rankInfo: AffiliateRankInfoDto | null;

  @ApiProperty({
    description: 'Số dư khả dụng',
    example: 1500000
  })
  availableBalance: number;

  @ApiProperty({
    description: 'Số tiền đang xử lý (tổng amount trong affiliate_withdraw_history có status PENDING hoặc INVOICE_NOT_UPLOADED)',
    example: 500000
  })
  processingAmount: number;

  @ApiProperty({
    description: 'Mã giới thiệu',
    example: 'NGUYENA123'
  })
  referralCode: string;

  @ApiProperty({
    description: 'Link giới thiệu',
    example: 'https://redai.vn/ref/NGUYENA123'
  })
  referralLink: string;
}
