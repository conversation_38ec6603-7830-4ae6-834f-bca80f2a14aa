### Test API tạo vai trò mới
POST http://localhost:3000/employee/roles
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "id": 10,
  "name": "Test Role",
  "description": "Vai trò test cho API",
  "permissionIds": [1, 2, 3]
}

### Test API tạo vai trò không có quyền
POST http://localhost:3000/employee/roles
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "id": 11,
  "name": "Simple Role",
  "description": "Vai trò đơn giản không có quyền"
}

### Test API lấy danh sách vai trò
GET http://localhost:3000/employee/roles
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test API lấy thông tin vai trò theo ID
GET http://localhost:3000/employee/roles/10
Authorization: Bearer YOUR_JWT_TOKEN_HERE
