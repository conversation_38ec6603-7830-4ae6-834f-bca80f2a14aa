# SepayHub Integration Troubleshooting

## Lỗi 401 Unauthorized khi refresh token

### Nguyên nhân có thể:

1. **Credentials không đúng hoặc thiếu**
   - API Key không được cấu hình
   - Secret Key không được cấu hình  
   - Credentials sai format

2. **Credentials đã hết hạn**
   - API Key/Secret Key đã bị revoke
   - Tài khoản SepayHub bị khóa

3. **Cấu hình môi trường sai**
   - Environment variables không được load
   - File .env thiếu hoặc sai đường dẫn

### Cách khắc phục:

#### 1. Kiểm tra Environment Variables

```bash
# Kiểm tra file .env
cat .env | grep SEPAY

# Hoặc kiểm tra trong runtime
node -e "console.log(process.env.SEPAY_HUB_API_KEY)"
```

#### 2. Chạy script kiểm tra cấu hình

```bash
# Chạy script check config
npx ts-node scripts/check-sepay-config.ts
```

#### 3. <PERSON><PERSON><PERSON> tra credentials với SepayHub

- Đăng nhập vào portal SepayHub
- Kiểm tra API credentials
- Tạo lại credentials nếu cần

#### 4. Test API trực tiếp

```bash
# Test với curl
curl -X POST https://partner-api.sepay.vn/merchant/v1/token/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic $(echo -n 'API_KEY:SECRET_KEY' | base64)"
```

### Environment Variables cần thiết:

```env
SEPAY_HUB_API_URL=https://partner-api.sepay.vn/merchant/v1
SEPAY_HUB_API_KEY=your_api_key_here
SEPAY_HUB_SECRET_KEY=your_secret_key_here
```

### Logs để debug:

Khi gặp lỗi, kiểm tra logs sau:

```
[SepayHubService] API Key exists: true/false
[SepayHubService] Secret Key exists: true/false  
[SepayHubService] API Key length: X
[SepayHubService] Secret Key length: Y
[SepayHubService] Refresh token failed - Status: 401
[SepayHubService] Refresh token failed - Response: {...}
```

### Liên hệ hỗ trợ:

Nếu vẫn gặp lỗi sau khi kiểm tra:
1. Liên hệ team SepayHub để kiểm tra tài khoản
2. Cung cấp logs chi tiết
3. Kiểm tra whitelist IP nếu có
