# Luồng Thanh Toán R-Point với Redis Cache

## Tổng quan
Hệ thống sử dụng Redis để cache trạng thái giao dịch nhằm cải thiện performance và trải nghiệm người dùng khi kiểm tra trạng thái thanh toán.

## Luồng hoạt động chi tiết

### 1. Tạo giao dịch mua R-Point
**Endpoint:** `POST /user/r-point/payment/purchase`

```mermaid
sequenceDiagram
    participant U as User
    participant API as Payment API
    participant DB as Database
    participant R as Redis
    participant S as SePay

    U->>API: POST /purchase (pointAmount, pointId)
    API->>DB: Tạo transaction (status: PENDING)
    API->>R: Lưu transaction vào Redis (TTL: 30min)
    API->>S: Tạo QR code thanh toán
    API->>U: Trả về QR code + transactionId
```

**Response:**
```json
{
  "success": true,
  "message": "Tạo giao dịch mua R-Point thành công",
  "result": {
    "id": 123,
    "userId": 1,
    "amount": 100000,
    "pointsAmount": 100,
    "status": "PENDING",
    "qrCodeUrl": "data:image/png;base64,..."
  }
}
```

### 2. User thanh toán qua ngân hàng
```mermaid
sequenceDiagram
    participant U as User
    participant B as Bank
    participant S as SePay
    participant W as Webhook

    U->>B: Chuyển khoản với nội dung "REDAI123SEPAY"
    B->>S: Thông báo giao dịch
    S->>W: Gửi webhook notification
```

### 3. Xử lý webhook (ĐÃ FIX)
**Endpoint:** `POST /r-point/webhook/ipn`

```mermaid
sequenceDiagram
    participant S as SePay
    participant W as Webhook Service
    participant DB as Database
    participant R as Redis
    participant U as User Service

    S->>W: POST webhook data
    W->>W: Validate API key
    W->>W: Parse transaction ID từ content
    W->>DB: Tìm transaction (status: PENDING)
    W->>W: Validate amount & transfer type
    W->>DB: Update transaction (status: CONFIRMED)
    W->>R: Update Redis (status: CONFIRMED) ← FIX
    W->>U: Cộng R-Point cho user
    W->>S: Return success response
```

**Webhook Request:**
```json
{
  "transferType": "in",
  "transferAmount": 100000,
  "content": "REDAI123SEPAY",
  "referenceCode": "REF123456",
  "transactionDate": "2024-01-15 10:30:00"
}
```

### 4. Frontend kiểm tra trạng thái
**Endpoint:** `GET /user/r-point/payment/status/{transactionId}`

```mermaid
sequenceDiagram
    participant F as Frontend
    participant API as Payment API
    participant R as Redis
    participant DB as Database

    F->>API: GET /status/123
    API->>R: Kiểm tra Redis cache
    
    alt Có trong Redis
        R->>API: Trả về transaction data
        API->>F: Return cached data (status: CONFIRMED)
    else Không có trong Redis
        API->>DB: Query database
        DB->>API: Transaction data
        API->>R: Lưu vào Redis (TTL: 30min)
        API->>F: Return database data
    end
```

## Code Implementation

### WebhookUserService (Đã fix)
```typescript
@Injectable()
export class WebhookUserService {
  constructor(
    private readonly transactionRepository: PointPurchaseTransactionRepository,
    private readonly transactionRedisService: TransactionRedisService, // ← Inject Redis service
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async processWebhook(webhookRequest: WebhookRequestDto): Promise<WebhookResponseDto> {
    // ... validation logic ...

    // Cập nhật database
    await this.updateTransaction(transaction, webhookRequest);

    // ← FIX: Cập nhật Redis
    await this.transactionRedisService.updateTransactionStatus(
      transaction.id,
      TransactionStatus.CONFIRMED
    );

    // Cộng điểm cho user
    await this.addPointsToUser(user, transaction.pointsAmount);

    return { success: true, message: 'Webhook processed successfully' };
  }
}
```

### TransactionRedisService
```typescript
@Injectable()
export class TransactionRedisService {
  private readonly transactionPrefix = 'r-point:transaction:';
  private readonly expiryTimeInSeconds = 30 * 60; // 30 phút

  async updateTransactionStatus(transactionId: number, status: TransactionStatus): Promise<void> {
    const key = `${this.transactionPrefix}${transactionId}`;
    const transactionDataStr = await this.redisService.get(key);

    if (transactionDataStr) {
      const transactionData = JSON.parse(transactionDataStr);
      transactionData.status = status;
      transactionData.updatedAt = Math.floor(Date.now() / 1000);

      await this.redisService.setWithExpiry(key, JSON.stringify(transactionData), this.expiryTimeInSeconds);
    }
  }
}
```

## Lợi ích của việc fix

### Trước khi fix:
- ❌ Webhook chỉ cập nhật database
- ❌ Redis vẫn có status `PENDING`
- ❌ Frontend phải đợi lâu để thấy status `CONFIRMED`
- ❌ User experience kém

### Sau khi fix:
- ✅ Webhook cập nhật cả database và Redis
- ✅ Redis có status `CONFIRMED` ngay lập tức
- ✅ Frontend thấy status cập nhật ngay
- ✅ User experience tốt hơn

## Monitoring & Debugging

### Kiểm tra Redis
```bash
# Kết nối Redis CLI
redis-cli

# Kiểm tra key transaction
KEYS r-point:transaction:*

# Xem nội dung transaction
GET r-point:transaction:123

# Kiểm tra TTL
TTL r-point:transaction:123
```

### Log để debug
```typescript
this.logger.debug(`Đã cập nhật trạng thái giao dịch ${transactionId} thành ${status} trong Redis`);
```

## Error Handling
- Nếu Redis không available, webhook vẫn hoạt động bình thường
- TransactionRedisService không throw error để không ảnh hưởng luồng chính
- API checkTransactionStatus có fallback về database nếu Redis không có data
