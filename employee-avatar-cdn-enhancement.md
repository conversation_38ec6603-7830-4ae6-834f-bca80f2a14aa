# Employee Avatar CDN URL Enhancement

## Tổng quan thay đổi

### 🔧 **Vấn đề trước đây:**
- API lấy danh sách employee trả về trường `avatar` chỉ là **S3 key** (ví dụ: `123/employee-avatar/avatar-123-1672531200000-uuid.jpg`)
- Frontend phải tự xử lý để tạo URL đầy đủ
- Không c<PERSON> signed URL với thời hạn bảo mật

### ✅ **Gi<PERSON>i pháp đã thực hiện:**
- **Inject CdnService** vào EmployeeService
- **Tự động convert S3 key thành CDN URL** có chữ ký bảo mật
- **Trả về URL đầy đủ** cho frontend sử dụng ngay

---

## Code Changes

### 1. Import CdnService
```typescript
// src/modules/employee/services/employee.service.ts
import { CdnService } from '@/shared/services/cdn.service';
```

### 2. Inject CdnService vào constructor
```typescript
constructor(
  private readonly employeeRepository: EmployeeRepository,
  private readonly employeeHasRoleRepository: EmployeeHasRoleRepository,
  private readonly employeeRoleRepository: EmployeeRoleRepository,
  private readonly permissionRepository: PermissionRepository,
  private readonly s3Service: S3Service,
  private readonly cdnService: CdnService, // ← Thêm inject
  private readonly passwordService: EmployeePasswordService,
) {}
```

### 3. Thêm method helper generateAvatarUrl
```typescript
/**
 * Tạo URL CDN cho avatar từ avatar key
 * @param avatarKey Key của avatar trên S3
 * @returns URL CDN đầy đủ hoặc undefined nếu không có avatar
 * @private
 */
private generateAvatarUrl(avatarKey: string | null): string | undefined {
  if (!avatarKey) {
    return undefined;
  }

  try {
    // Sử dụng CDN service để tạo URL với thời hạn 24 giờ
    return this.cdnService.generateUrlView(avatarKey, TimeIntervalEnum.ONE_DAY) || undefined;
  } catch (error) {
    this.logger.warn(`Không thể tạo URL CDN cho avatar key ${avatarKey}: ${error.message}`);
    return undefined;
  }
}
```

### 4. Cập nhật method findAll
```typescript
async findAll(queryDto: EmployeeQueryDto): Promise<PaginatedResult<Employee>> {
  try {
    const result = await this.employeeRepository.findAll(queryDto);
    
    // Xử lý avatar URL cho từng nhân viên
    const employeesWithAvatarUrl = result.items.map(employee => ({
      ...employee,
      avatar: this.generateAvatarUrl(employee.avatar || null) // ← Convert key thành URL
    }));

    return {
      ...result,
      items: employeesWithAvatarUrl
    };
  } catch (error) {
    // ... error handling
  }
}
```

---

## API Response

### Trước khi fix:
```json
{
  "success": true,
  "message": "Lấy danh sách nhân viên thành công",
  "result": {
    "items": [
      {
        "id": 123,
        "fullName": "Nguyễn Văn A",
        "email": "<EMAIL>",
        "phoneNumber": "0987654321",
        "address": "Hà Nội",
        "avatar": "123/employee-avatar/avatar-123-1672531200000-uuid.jpg", // ← Chỉ là S3 key
        "enable": true,
        "createdAt": 1672531200,
        "updatedAt": 1672531200
      }
    ],
    "meta": {
      "totalItems": 1,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### Sau khi fix:
```json
{
  "success": true,
  "message": "Lấy danh sách nhân viên thành công",
  "result": {
    "items": [
      {
        "id": 123,
        "fullName": "Nguyễn Văn A",
        "email": "<EMAIL>",
        "phoneNumber": "0987654321",
        "address": "Hà Nội",
        "avatar": "https://cdn.redai.vn/123/employee-avatar/avatar-123-1672531200000-uuid.jpg?expires=1672617600&signature=abc123def456", // ← URL đầy đủ có chữ ký
        "enable": true,
        "createdAt": 1672531200,
        "updatedAt": 1672531200
      }
    ],
    "meta": {
      "totalItems": 1,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

---

## CDN Service Details

### generateUrlView Method
```typescript
generateUrlView(key: string, time: TimeIntervalEnum): string | null {
  if (!key || key.length === 0) {
    return null;
  }

  try {
    const unixTime = Math.floor(Date.now() / 1000);
    const expires = unixTime + time;
    const text = `${expires}|${key}`;

    // Tạo chữ ký HMAC-SHA1
    const hmac = crypto.createHmac('sha1', this.secretKey);
    hmac.update(text);
    const signature = hmac.digest('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');

    // Trả về URL với chữ ký và thời gian hết hạn
    return `${this.cdnUrl}/${key}?expires=${expires}&signature=${signature}`;
  } catch (error) {
    this.logger.error(`Error generating signed URL: ${error.message}`);
    throw new AppException(ErrorCode.CDN_URL_GENERATION_ERROR, error.message);
  }
}
```

### URL Structure
```
https://cdn.redai.vn/{s3_key}?expires={timestamp}&signature={hmac_signature}
```

- **cdn.redai.vn**: CDN domain
- **s3_key**: S3 object key (ví dụ: `123/employee-avatar/avatar-123-1672531200000-uuid.jpg`)
- **expires**: Unix timestamp khi URL hết hạn
- **signature**: HMAC-SHA1 signature để bảo mật

---

## Test Cases

### Test 1: Lấy danh sách employee có avatar
```bash
curl -X GET "http://localhost:3000/admin/employee?page=1&limit=10" \
  -H "Authorization: Bearer {JWT_TOKEN}"

# Expected: Response có avatar URL đầy đủ với CDN domain và signature
```

### Test 2: Lấy danh sách employee không có avatar
```bash
curl -X GET "http://localhost:3000/admin/employee?page=1&limit=10" \
  -H "Authorization: Bearer {JWT_TOKEN}"

# Expected: Response có avatar = undefined cho employee không có avatar
```

### Test 3: Kiểm tra URL có thời hạn
```bash
# Lấy avatar URL từ API response
AVATAR_URL="https://cdn.redai.vn/123/employee-avatar/avatar-123-1672531200000-uuid.jpg?expires=1672617600&signature=abc123def456"

# Truy cập URL trong thời hạn
curl -I "$AVATAR_URL"
# Expected: 200 OK

# Đợi URL hết hạn (24 giờ) rồi truy cập lại
# Expected: 403 Forbidden hoặc 404 Not Found
```

---

## Security Features

### 1. **Signed URLs**
- Mỗi URL có chữ ký HMAC-SHA1 để ngăn chặn truy cập trái phép
- Chỉ server có secret key mới tạo được URL hợp lệ

### 2. **Time-based Expiration**
- URL có thời hạn 24 giờ (`TimeIntervalEnum.ONE_DAY`)
- Sau thời hạn, URL sẽ không thể truy cập được

### 3. **Domain Restriction**
- Chỉ có thể truy cập qua CDN domain được cấu hình
- Ngăn chặn hotlinking từ domain khác

---

## Benefits

### Trước khi fix:
- ❌ Frontend nhận S3 key thô
- ❌ Phải tự xử lý tạo URL
- ❌ Không có bảo mật URL
- ❌ Có thể bị hotlinking

### Sau khi fix:
- ✅ Frontend nhận URL đầy đủ sẵn sàng sử dụng
- ✅ URL có chữ ký bảo mật
- ✅ URL có thời hạn tự động hết hạn
- ✅ Tối ưu performance với CDN
- ✅ Ngăn chặn truy cập trái phép

---

## Configuration

### Environment Variables
```env
# CDN Configuration
CDN_URL=https://cdn.redai.vn
CDN_SECRET_KEY=your-secret-key-here
```

### Config File
```typescript
// src/config/storage.config.ts
export interface StorageConfig {
  cdn: {
    url: string;
    secretKey: string;
  };
}
```

---

## Error Handling

### Lỗi khi tạo CDN URL
```typescript
// Log warning nhưng không throw error
this.logger.warn(`Không thể tạo URL CDN cho avatar key ${avatarKey}: ${error.message}`);
return undefined; // Trả về undefined thay vì crash
```

### Fallback behavior
- Nếu không tạo được CDN URL → trả về `undefined`
- Frontend có thể hiển thị avatar placeholder
- Không ảnh hưởng đến việc lấy danh sách employee
