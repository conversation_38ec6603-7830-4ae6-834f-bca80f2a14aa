import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, Observable } from 'rxjs';
import { timeout } from 'rxjs/operators';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { KNOWLEDGE_FILE_ERROR_CODES } from '@modules/data/knowledge-files/exceptions';
import { AxiosRequestConfig } from 'axios';
import { backOff } from 'exponential-backoff';

/**
 * Interface for RAG API file processing request
 */
export interface RagFileProcessRequest {
  chunk_overlap: number;
  chunk_size: number;
  key: string;
  vector_store_id?: string;
}

/**
 * Interface for RAG API file processing response
 */
export interface RagFileProcessResponse {
  chunk_overlap: number;
  chunk_size: number;
  file_id: string;
  filename: string;
  message: string;
  status: string;
  vector_store_id?: string;
  vector_store_name?: string;
}

/**
 * Interface for RAG API file progress response
 */
export interface RagFileProgressResponse {
  file_id: string;
  progress: number;
  status: string;
  message?: string;
  error?: string;
}

/**
 * Interface for RAG API media processing request
 */
export interface RagMediaProcessRequest {
  name: string;
  description?: string;
  tags: string[];
  storage_key: string;
  chunk_size: number;
  chunk_overlap: number;
}

/**
 * Interface for RAG API media processing response
 */
export interface RagMediaProcessResponse {
  media_id: string;
  message: string;
  status: string;
  task_id: string;
  chunk_size: number;
  chunk_overlap: number;
  name: string;
}

/**
 * Service chuyên xử lý tập tin sử dụng RAG API
 */
@Injectable()
export class RagFileProcessingService {
  private readonly logger = new Logger(RagFileProcessingService.name);
  private readonly apiKey: string;
  private readonly baseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.apiKey = this.configService.get<string>('RAG_API', '');
    this.baseUrl = this.configService.get<string>('FAST_API_URL', 'http://localhost:8000/');

    if (!this.apiKey) {
      this.logger.warn('RAG API key is not configured');
    }

    if (!this.baseUrl) {
      this.logger.warn('Fast API URL is not configured');
    }
  }

  /**
   * Tạo config cho request API với X-API-Key header
   * @param timeout Timeout cho request (ms), mặc định 10 giây
   * @returns AxiosRequestConfig
   */
  private createRequestConfig(timeout: number = 10000): AxiosRequestConfig {
    return {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey,
        'Connection': 'close', // Đóng connection ngay sau response
      },
      timeout, // Đặt timeout để tránh đợi quá lâu
      maxRedirects: 0, // Không follow redirect
    };
  }

  /**
   * Tạo nhiều vector stores thông qua RAG API
   * @param vectorStores Danh sách vector stores cần tạo
   * @returns Response từ RAG API với thông tin các vector stores đã tạo
   * @throws AppException nếu có lỗi khi gửi request
   */
  async createBulkVectorStores(vectorStores: { name: string }[]): Promise<{
    created_count: number;
    message: string;
    success: boolean;
    vector_stores: Array<{
      created_at: number;
      id: string;
      name: string;
      storage: number;
      update_at: number;
    }>;
  }> {
    try {
      this.logger.log(`Bắt đầu tạo ${vectorStores.length} vector stores thông qua RAG API`);

      const endpoint = 'api/v1/user/vector-stores/bulk';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const data = {
        vector_stores: vectorStores
      };

      this.logger.log(`Đang gửi request đến RAG API: ${fullUrl}`);
      this.logger.debug(`Request data: ${JSON.stringify(data)}`);

      // Sử dụng native fetch với AbortController
      const abortController = new AbortController();
      const timeoutId = setTimeout(() => abortController.abort(), 30000); // 30 giây timeout

      const response = await fetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          'Connection': 'close',
        },
        body: JSON.stringify(data),
        signal: abortController.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error(`RAG API trả về lỗi ${response.status}: ${errorText}`);
        throw new AppException(
          KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
          `Lỗi khi tạo vector stores từ RAG API: ${response.status} - ${errorText}`,
        );
      }

      const result = await response.json();
      this.logger.log(`Đã tạo thành công ${result.created_count} vector stores từ RAG API`);
      this.logger.debug(`RAG API response: ${JSON.stringify(result)}`);

      return result;
    } catch (error) {
      if (error.name === 'AbortError') {
        this.logger.error('Request tạo bulk vector stores bị timeout');
        throw new AppException(
          KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
          'Timeout khi tạo vector stores từ RAG API',
        );
      }

      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi tạo bulk vector stores: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
        `Lỗi khi tạo vector stores: ${error.message}`,
      );
    }
  }

  /**
   * Gửi file đến RAG API để lấy file ID ngay lập tức (fire-and-forget)
   * @param storageKey S3 key của tập tin
   * @param chunkSize Kích thước của mỗi đoạn văn bản
   * @param chunkOverlap Độ chồng lấp giữa các đoạn
   * @param vectorStoreId ID của vector store để lưu trữ các đoạn (tùy chọn)
   * @returns File ID từ RAG API
   * @throws AppException nếu có lỗi khi gửi request
   */
  async processFileFromS3Key(
    storageKey: string,
    chunkSize: number = 2000,
    chunkOverlap: number = 100,
    vectorStoreId?: string,
  ): Promise<RagFileProcessResponse> {
    try {
      this.logger.log(`Bắt đầu xử lý file từ S3 key: ${storageKey}`);

      // Gọi API để xử lý file từ S3 key trực tiếp
      const endpoint = 'api/files/process-url';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      this.logger.log(`Đang gửi storage key đến RAG API: ${storageKey}`);

      const data: RagFileProcessRequest = {
        key: storageKey, // Gửi storage key trực tiếp như RAG API mong đợi
        chunk_size: chunkSize,
        chunk_overlap: chunkOverlap,
      };

      // Thêm vector_store_id vào request nếu được cung cấp
      if (vectorStoreId) {
        data.vector_store_id = vectorStoreId;
        this.logger.log(`Đang gửi file đến vector store: ${vectorStoreId}`);
      }

      // Sử dụng native fetch với AbortController để có control tốt hơn
      const abortController = new AbortController();

      // Timeout sau 3 giây
      const timeoutId = setTimeout(() => abortController.abort(), 3000);

      try {
        const response = await fetch(fullUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': this.apiKey,
            'Connection': 'close',
          },
          body: JSON.stringify(data),
          signal: abortController.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const responseData = await response.json() as RagFileProcessResponse;

        this.logger.log(`Đã nhận file ID từ RAG API: ${responseData.file_id} (status: ${response.status})`);
        return responseData;
      } catch (fetchError) {
        clearTimeout(timeoutId);
        if (fetchError.name === 'AbortError') {
          throw new Error('Request was aborted due to timeout');
        }
        throw fetchError;
      }

    } catch (error) {
      this.logger.error(`Lỗi khi xử lý file từ S3 key: ${error.message}`, error.stack);

      // Xử lý lỗi chi tiết hơn
      if (error.response) {
        const status = error.response.status;
        const message = error.response.data?.message || error.message;

        this.logger.error(`RAG API response error: ${status} - ${message}`);

        if (status === 401 || status === 403) {
          throw new AppException(
            ErrorCode.TOKEN_NOT_FOUND,
            'Không có quyền truy cập RAG API',
          );
        } else if (status === 400) {
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            `Dữ liệu gửi đến RAG API không hợp lệ: ${message}`,
          );
        } else if (status >= 500) {
          throw new AppException(
            ErrorCode.EXTERNAL_SERVICE_ERROR,
            `RAG API gặp lỗi nội bộ: ${message}`,
          );
        }
      }

      // Xử lý lỗi kết nối
      if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể kết nối đến RAG API. Vui lòng kiểm tra cấu hình.',
        );
      }

      // Lỗi khác
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi xử lý tập tin từ S3: ${error.message}`,
      );
    }
  }

  /**
   * Xử lý tập tin từ URL S3
   * @param url URL của tập tin trên S3
   * @param chunkSize Kích thước của mỗi đoạn văn bản
   * @param chunkOverlap Độ chồng lấp giữa các đoạn
   * @param vectorStoreId ID của vector store để lưu trữ các đoạn (tùy chọn)
   * @returns Thông tin về quá trình xử lý tập tin
   * @throws AppException nếu có lỗi khi xử lý tập tin
   */
  async processFileFromUrl(
    url: string,
    chunkSize: number = 2000,
    chunkOverlap: number = 100,
    vectorStoreId?: string,
  ): Promise<RagFileProcessResponse> {
    try {
      this.logger.log(`Xử lý tập tin từ URL: ${url}`);

      const endpoint = 'api/files/process-url';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      // Nếu URL là storage key (không có http), sử dụng trực tiếp
      // Nếu URL là full URL, extract storage key
      let storageKey = url;
      if (url.startsWith('http')) {
        // Extract storage key từ full URL
        const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
        storageKey = url.replace(`${cdnUrl}/`, '');
        this.logger.log(`Đã extract storage key từ URL: ${storageKey}`);
      }

      const data: RagFileProcessRequest = {
        key: storageKey, // Gửi storage key thay vì URL
        chunk_size: chunkSize,
        chunk_overlap: chunkOverlap,
      };

      // Thêm vector_store_id vào request nếu được cung cấp
      if (vectorStoreId) {
        data.vector_store_id = vectorStoreId;
        this.logger.log(`Đang gửi file đến vector store: ${vectorStoreId}`);
      }

      // Tạo AbortController để có thể cancel request
      const abortController = new AbortController();
      const config = {
        ...this.createRequestConfig(3000),
        signal: abortController.signal, // Thêm signal để có thể abort
      };

      // Chỉ chờ HTTP response 202 với file ID, không đợi processing
      const response = await firstValueFrom(
        this.httpService.post<RagFileProcessResponse>(fullUrl, data, config).pipe(
          timeout(3000) // RxJS timeout để đảm bảo không đợi quá lâu
        )
      );

      this.logger.log(`Bắt đầu xử lý tập tin: ${response.data.file_id}`);
      return response.data;

    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý tập tin từ URL: ${error.message}`,
        error.stack,
      );

      if (error.response) {
        this.logger.error(`Mã lỗi: ${error.response.status}`);
        this.logger.error(`Dữ liệu lỗi: ${JSON.stringify(error.response.data)}`);

        if (error.response.status === 401 || error.response.status === 403) {
          throw new AppException(
            ErrorCode.TOKEN_NOT_FOUND,
            'Không có quyền truy cập RAG API',
          );
        }
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi xử lý tập tin: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra tiến độ xử lý tập tin
   * @param fileId ID của tập tin đang xử lý
   * @returns Thông tin về tiến độ xử lý tập tin
   * @throws AppException nếu có lỗi khi kiểm tra tiến độ
   */
  async checkFileProgress(fileId: string): Promise<RagFileProgressResponse> {
    try {
      this.logger.log(`Kiểm tra tiến độ xử lý tập tin: ${fileId}`);

      const endpoint = `api/files/${fileId}/progress`;
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const config = this.createRequestConfig();

      const response = await firstValueFrom(
        this.httpService.get<RagFileProgressResponse>(fullUrl, config)
      );

      this.logger.log(`Tiến độ xử lý tập tin: ${response.data.status}, ${response.data.progress}%`);
      return response.data;

    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra tiến độ xử lý tập tin: ${error.message}`,
        error.stack,
      );

      if (error.response?.status === 404) {
        throw new AppException(
          ErrorCode.NOT_FOUND,
          `Không tìm thấy thông tin tiến độ cho file ID: ${fileId}`,
        );
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi kiểm tra tiến độ xử lý tập tin: ${error.message}`,
      );
    }
  }

  /**
   * Chờ cho đến khi tập tin được xử lý hoàn tất
   * @param fileId ID của tập tin đang xử lý
   * @param maxAttempts Số lần thử tối đa
   * @param initialDelayMs Thời gian chờ ban đầu (ms)
   * @returns Thông tin về tiến độ xử lý tập tin khi hoàn tất
   * @throws AppException nếu có lỗi khi chờ xử lý tập tin
   */
  async waitForFileProcessing(
    fileId: string,
    maxAttempts: number = 30,
    initialDelayMs: number = 2000,
  ): Promise<RagFileProgressResponse> {
    try {
      this.logger.log(`Bắt đầu chờ xử lý file: ${fileId} (max attempts: ${maxAttempts})`);

      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          const progress = await this.checkFileProgress(fileId);

          this.logger.debug(`File ${fileId} progress: ${progress.progress}% - ${progress.status}`);

          if (progress.status === 'completed') {
            this.logger.log(`File ${fileId} đã xử lý xong thành công`);
            return progress;
          }

          if (progress.status === 'failed' || progress.status === 'error') {
            const errorMsg = progress.error || progress.message || 'Lỗi không xác định';
            this.logger.error(`File ${fileId} xử lý thất bại: ${errorMsg}`);
            throw new Error(`Xử lý tập tin thất bại: ${errorMsg}`);
          }

          // Nếu chưa hoàn tất, ném lỗi để tiếp tục thử lại
          throw new Error(`Tập tin đang được xử lý (${progress.progress}%): ${progress.message}`);
        },
        {
          numOfAttempts: maxAttempts,
          startingDelay: initialDelayMs,
          timeMultiple: 1.5,
          delayFirstAttempt: false,
          maxDelay: 30000, // Tối đa 30 giây giữa các lần thử
        },
      );

      return result;

    } catch (error) {
      this.logger.error(
        `Lỗi khi chờ xử lý tập tin: ${error.message}`,
        error.stack,
      );

      // Kiểm tra xem có phải lỗi timeout không
      if (error.message.includes('Tập tin đang được xử lý')) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Timeout: Tập tin vẫn đang được xử lý sau ${maxAttempts} lần thử. Vui lòng kiểm tra lại sau.`,
        );
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi chờ xử lý tập tin: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra trạng thái kết nối đến RAG API
   * @returns true nếu kết nối thành công
   */
  async checkConnection(): Promise<boolean> {
    try {
      const endpoint = 'health';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const config = this.createRequestConfig();

      await firstValueFrom(
        this.httpService.get(fullUrl, config)
      );

      this.logger.log('Kết nối đến RAG API thành công');
      return true;

    } catch (error) {
      this.logger.error(`Không thể kết nối đến RAG API: ${error.message}`);
      return false;
    }
  }

  /**
   * Lấy thông tin về một file đã xử lý
   * @param fileId ID của file
   * @returns Thông tin file hoặc null nếu không tìm thấy
   */
  async getFileInfo(fileId: string): Promise<any> {
    try {
      const endpoint = `api/files/${fileId}`;
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const config = this.createRequestConfig();

      const response = await firstValueFrom(
        this.httpService.get(fullUrl, config)
      );

      return response.data;

    } catch (error) {
      if (error.response?.status === 404) {
        return null;
      }

      this.logger.error(`Lỗi khi lấy thông tin file ${fileId}: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy thông tin file: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách vector stores từ RAG API
   * @param params Query parameters
   * @returns Danh sách vector stores từ RAG API
   */
  async getVectorStores(params: {
    skip?: number;
    limit?: number;
    sort_by?: string;
    sort_order?: string;
    search?: string;
  }): Promise<{
    total: number;
    vector_stores: Array<{
      created_at: number;
      id: string;
      name: string;
      storage: number;
      update_at: number;
    }>;
  }> {
    try {
      this.logger.log('Lấy danh sách vector stores từ RAG API');

      const endpoint = 'api/v1/user/vector-stores/';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      // Tạo query string
      const queryParams = new URLSearchParams();
      if (params.skip !== undefined) queryParams.append('skip', params.skip.toString());
      if (params.limit !== undefined) queryParams.append('limit', params.limit.toString());
      if (params.sort_by) queryParams.append('sort_by', params.sort_by);
      if (params.sort_order) queryParams.append('sort_order', params.sort_order);
      if (params.search) queryParams.append('search', params.search);

      const finalUrl = queryParams.toString() ? `${fullUrl}?${queryParams.toString()}` : fullUrl;

      this.logger.log(`Final URL: ${finalUrl}`);
      this.logger.log(`Query params: ${JSON.stringify(Object.fromEntries(queryParams))}`);

      const response = await fetch(finalUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          'Connection': 'close',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.logger.log(`Đã lấy ${result.total} vector stores từ RAG API`);

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách vector stores: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
        `Lỗi khi lấy danh sách vector stores: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết vector store từ RAG API
   * @param vectorStoreId ID của vector store
   * @returns Thông tin chi tiết vector store
   */
  async getVectorStoreDetail(vectorStoreId: string): Promise<{
    created_at: number;
    id: string;
    name: string;
    storage: number;
    update_at: number;
  }> {
    try {
      this.logger.log(`Lấy thông tin chi tiết vector store ${vectorStoreId} từ RAG API`);

      const endpoint = `api/v1/user/vector-stores/${vectorStoreId}`;
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          'Connection': 'close',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new AppException(
            KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
            'Vector store không tồn tại trên RAG API',
          );
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.logger.log(`Đã lấy thông tin vector store ${vectorStoreId} từ RAG API`);

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy thông tin vector store: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DETAIL_ERROR,
        `Lỗi khi lấy thông tin vector store: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật vector store trên RAG API
   * @param vectorStoreId ID của vector store
   * @param data Dữ liệu cập nhật
   * @returns Thông tin vector store đã cập nhật
   */
  async updateVectorStore(vectorStoreId: string, data: { name?: string }): Promise<{
    created_at: number;
    id: string;
    name: string;
    storage: number;
    update_at: number;
  }> {
    try {
      this.logger.log(`Cập nhật vector store ${vectorStoreId} trên RAG API`);

      const endpoint = `api/v1/user/vector-stores/${vectorStoreId}`;
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const response = await fetch(fullUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          'Connection': 'close',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new AppException(
            KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
            'Vector store không tồn tại trên RAG API',
          );
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.logger.log(`Đã cập nhật vector store ${vectorStoreId} trên RAG API`);

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật vector store: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
        `Lỗi khi cập nhật vector store: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều vector stores trên RAG API
   * @param vectorStoreIds Danh sách ID các vector stores cần xóa
   * @returns Kết quả xóa từ RAG API
   */
  async deleteBulkVectorStores(vectorStoreIds: string[]): Promise<{
    created_count: number;
    message: string;
    success: boolean;
    vector_stores: Array<{
      created_at: number;
      id: string;
      name: string;
      storage: number;
      update_at: number;
    }>;
  }> {
    try {
      this.logger.log(`Xóa ${vectorStoreIds.length} vector stores trên RAG API`);

      const endpoint = 'api/v1/user/vector-stores/bulk/delete';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const data = {
        vs_ids: vectorStoreIds
      };

      const response = await fetch(fullUrl, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          'Connection': 'close',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.logger.log(`Đã xóa ${result.created_count} vector stores trên RAG API`);

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa vector stores: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DELETE_ERROR,
        `Lỗi khi xóa vector stores: ${error.message}`,
      );
    }
  }

  /**
   * Tạo nhiều vector stores trên RAG API (fire-and-forget)
   * Chỉ gọi API, không nhận response, không lưu DB
   * @param vectorStores Danh sách vector stores cần tạo
   */
  async createBulkVectorStoresFireAndForget(vectorStores: { name: string }[]): Promise<void> {
    try {
      this.logger.log(`Tạo ${vectorStores.length} vector stores trên RAG API (fire-and-forget)`);

      const endpoint = 'api/v1/user/vector-stores/bulk';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const data = {
        vector_stores: vectorStores
      };

      // Fire-and-forget: không đợi response
      fetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          'Connection': 'close',
        },
        body: JSON.stringify(data),
      }).catch(error => {
        this.logger.warn(`Fire-and-forget create failed for vector stores: ${error.message}`);
      });

      this.logger.log(`Đã gửi request tạo ${vectorStores.length} vector stores (fire-and-forget)`);
    } catch (error) {
      this.logger.error(`Lỗi khi gửi request tạo vector stores: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
        `Lỗi khi gửi request tạo vector stores: ${error.message}`,
      );
    }
  }



  /**
   * Xóa nhiều vector stores trên RAG API (fire-and-forget)
   * Chỉ gọi API, không nhận response, không lưu DB
   * @param vectorStoreIds Danh sách ID các vector stores cần xóa
   */
  async deleteBulkVectorStoresFireAndForget(vectorStoreIds: string[]): Promise<void> {
    try {
      this.logger.log(`Xóa ${vectorStoreIds.length} vector stores trên RAG API (fire-and-forget)`);

      const endpoint = 'api/v1/user/vector-stores/bulk/delete';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const data = {
        vs_ids: vectorStoreIds
      };

      // Fire-and-forget: không đợi response
      fetch(fullUrl, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          'Connection': 'close',
        },
        body: JSON.stringify(data),
      }).catch(error => {
        this.logger.warn(`Fire-and-forget delete failed for vector stores: ${error.message}`);
      });

      this.logger.log(`Đã gửi request xóa ${vectorStoreIds.length} vector stores (fire-and-forget)`);
    } catch (error) {
      this.logger.error(`Lỗi khi gửi request xóa vector stores: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DELETE_ERROR,
        `Lỗi khi gửi request xóa vector stores: ${error.message}`,
      );
    }
  }

  /**
   * Theo dõi tiến độ xử lý file từ RAG API (SSE)
   * @param fileId ID của file cần theo dõi
   * @returns Observable stream của tracking events
   */
  async trackFileProgress(fileId: string): Promise<Observable<any>> {
    try {
      this.logger.log(`Bắt đầu tracking file ${fileId} từ RAG API`);

      const endpoint = `api/tracking/progress/${fileId}`;
      const fullUrl = `${this.baseUrl}${endpoint}`;

      return new Observable(observer => {
        // EventSource không hỗ trợ custom headers, sử dụng fetch với SSE
        const controller = new AbortController();

        fetch(fullUrl, {
          method: 'GET',
          headers: {
            'X-API-Key': this.apiKey,
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache',
          },
          signal: controller.signal,
        }).then(response => {
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const reader = response.body?.getReader();
          if (!reader) {
            throw new Error('No response body reader available');
          }

          const decoder = new TextDecoder();

          const readStream = async () => {
            try {
              while (true) {
                const { done, value } = await reader.read();

                if (done) {
                  observer.complete();
                  break;
                }

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split('\n');

                for (const line of lines) {
                  if (line.startsWith('data: ')) {
                    try {
                      const data = JSON.parse(line.slice(6));

                      // Log chi tiết về event nhận được từ RAG API
                      this.logger.log(`📡 RAG SSE Event: ${data.file_id} - ${data.status} - ${data.progress}%`);
                      this.logger.log(`📡 RAG Message: ${data.message || 'No message'}`);

                      // Log metadata nếu có
                      if (data.metadata && Object.keys(data.metadata).length > 0) {
                        this.logger.log(`📡 RAG Metadata: ${JSON.stringify(data.metadata)}`);

                        // Đặc biệt chú ý storage update events
                        if (data.metadata.storage_updated) {
                          this.logger.log(`💾 STORAGE UPDATE EVENT: vs=${data.metadata.vector_store_id}, size=${data.metadata.storage_size}`);
                        }

                        if (data.metadata.processing_completed) {
                          this.logger.log(`🏁 PROCESSING COMPLETED: chunks=${data.metadata.chunks_count}, final_size=${data.metadata.final_storage_size}`);
                        }
                      } else {
                        this.logger.debug(`📡 No metadata in event`);
                      }

                      observer.next(data);

                      // Đóng connection khi hoàn thành hoặc lỗi
                      if (['completed', 'error', 'cancelled'].includes(data.status)) {
                        this.logger.log(`🔚 Closing SSE connection for ${data.file_id}: ${data.status}`);
                        controller.abort();
                        observer.complete();
                        return;
                      }
                    } catch (error) {
                      this.logger.error(`❌ Error parsing SSE data: ${error.message}`);
                      this.logger.error(`❌ Raw line: ${line}`);
                    }
                  }
                }
              }
            } catch (error) {
              if (error.name !== 'AbortError') {
                this.logger.error(`Stream reading error: ${error.message}`);
                observer.error(error);
              }
            }
          };

          readStream();
        }).catch(error => {
          this.logger.error(`Fetch error: ${error.message}`);
          observer.error(new AppException(
            KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR,
            'Lỗi kết nối với RAG API',
          ));
        });

        // Cleanup function
        return () => {
          controller.abort();
        };
      });
    } catch (error) {
      this.logger.error(`Error starting file tracking: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR,
        `Lỗi khi bắt đầu tracking file: ${error.message}`,
      );
    }
  }

  /**
   * Xử lý media với embedding cả text và ảnh
   * @param name Tên của media
   * @param description Mô tả của media
   * @param tags Tags của media
   * @param storageKey S3 storage key của media
   * @param chunkSize Kích thước chunk
   * @param chunkOverlap Độ chồng lấp chunk
   * @returns Media ID từ RAG API
   */
  async processMediaMixed(
    name: string,
    description: string | null,
    tags: string[],
    storageKey: string,
    chunkSize: number = 2000,
    chunkOverlap: number = 100,
  ): Promise<string | null> {
    try {
      this.logger.log(`Bắt đầu xử lý media mixed: ${name} với storage key: ${storageKey}`);

      const endpoint = 'api/media/process-mixed';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const data: RagMediaProcessRequest = {
        name,
        description: description || undefined,
        tags,
        storage_key: storageKey,
        chunk_size: chunkSize,
        chunk_overlap: chunkOverlap,
      };

      this.logger.log(`Đang gửi request đến RAG API: ${fullUrl}`);
      this.logger.debug(`Request data: ${JSON.stringify(data)}`);

      // Sử dụng native fetch với AbortController
      const abortController = new AbortController();
      const timeoutId = setTimeout(() => abortController.abort(), 30000); // 30 giây timeout

      try {
        const response = await fetch(fullUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': this.apiKey,
            'Connection': 'close',
          },
          body: JSON.stringify(data),
          signal: abortController.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const responseData = await response.json() as RagMediaProcessResponse;

        this.logger.log(`Đã nhận media_id từ RAG API: ${responseData.media_id} cho media "${name}"`);
        return responseData.media_id;

      } catch (fetchError) {
        clearTimeout(timeoutId);
        if (fetchError.name === 'AbortError') {
          throw new Error('Request was aborted due to timeout');
        }
        throw fetchError;
      }

    } catch (error) {
      this.logger.error(`Lỗi khi xử lý media mixed "${name}": ${error.message}`, error.stack);
      return null; // Trả về null thay vì throw để không ảnh hưởng flow chính
    }
  }

  /**
   * Xử lý media với embedding cả text và ảnh sử dụng API key cụ thể
   * @param name Tên của media
   * @param description Mô tả của media
   * @param tags Tags của media
   * @param storageKey S3 storage key của media
   * @param chunkSize Kích thước chunk
   * @param chunkOverlap Độ chồng lấp chunk
   * @param apiKey API key cụ thể của user
   * @returns Media ID từ RAG API
   */
  async processMediaMixedWithApiKey(
    name: string,
    description: string | null,
    tags: string[],
    storageKey: string,
    chunkSize: number = 2000,
    chunkOverlap: number = 100,
    apiKey: string,
  ): Promise<string | null> {
    try {
      this.logger.log(`Bắt đầu xử lý media mixed: ${name} với storage key: ${storageKey} và API key: ${apiKey.substring(0, 10)}...`);

      const endpoint = 'api/media/process-mixed';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const data: RagMediaProcessRequest = {
        name,
        description: description || undefined,
        tags,
        storage_key: storageKey,
        chunk_size: chunkSize,
        chunk_overlap: chunkOverlap,
      };

      this.logger.log(`Đang gửi request đến RAG API: ${fullUrl}`);
      this.logger.debug(`Request data: ${JSON.stringify(data)}`);

      // Sử dụng native fetch với AbortController
      const abortController = new AbortController();
      const timeoutId = setTimeout(() => abortController.abort(), 30000); // 30 giây timeout

      try {
        const response = await fetch(fullUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': apiKey, // Sử dụng API key của user
            'Connection': 'close',
          },
          body: JSON.stringify(data),
          signal: abortController.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const responseData = await response.json() as RagMediaProcessResponse;

        this.logger.log(`Đã nhận media_id từ RAG API: ${responseData.media_id} cho media "${name}"`);
        return responseData.media_id;

      } catch (fetchError) {
        clearTimeout(timeoutId);
        if (fetchError.name === 'AbortError') {
          throw new Error('Request was aborted due to timeout');
        }
        throw fetchError;
      }

    } catch (error) {
      this.logger.error(`Lỗi khi xử lý media mixed "${name}": ${error.message}`, error.stack);
      return null; // Trả về null thay vì throw để không ảnh hưởng flow chính
    }
  }

  /**
   * Theo dõi tiến độ xử lý media từ RAG API
   * @param mediaId ID của media cần theo dõi
   * @returns Observable stream của tracking events
   */
  async trackMediaProgress(mediaId: string): Promise<Observable<any>> {
    try {
      this.logger.log(`Bắt đầu tracking media progress: ${mediaId}`);

      // Sử dụng endpoint tracking tương tự như file nhưng cho media
      const endpoint = `api/media/${mediaId}/progress`;
      const fullUrl = `${this.baseUrl}${endpoint}`;

      return new Observable((observer) => {
        const controller = new AbortController();

        // Timeout sau 5 phút (300 giây)
        const timeoutId = setTimeout(() => {
          controller.abort();
          observer.error(new Error('Media tracking timeout after 5 minutes'));
        }, 300000);

        fetch(fullUrl, {
          method: 'GET',
          headers: {
            'X-API-Key': this.apiKey,
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache',
          },
          signal: controller.signal,
        }).then(response => {
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const reader = response.body?.getReader();
          if (!reader) {
            throw new Error('No response body reader available');
          }

          const decoder = new TextDecoder();

          const readStream = async () => {
            try {
              while (true) {
                const { done, value } = await reader.read();

                if (done) {
                  observer.complete();
                  break;
                }

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split('\n');

                for (const line of lines) {
                  if (line.startsWith('data: ')) {
                    try {
                      const data = JSON.parse(line.slice(6));
                      this.logger.debug(`📡 Media tracking event: ${JSON.stringify(data)}`);
                      observer.next(data);

                      // Auto-close khi hoàn thành hoặc lỗi
                      if (['completed', 'error', 'timeout'].includes(data.status)) {
                        clearTimeout(timeoutId);
                        observer.complete();
                        return;
                      }
                    } catch (error) {
                      this.logger.error(`❌ Error parsing media tracking data: ${error.message}`);
                      this.logger.error(`❌ Raw line: ${line}`);
                    }
                  }
                }
              }
            } catch (error) {
              if (error.name !== 'AbortError') {
                this.logger.error(`Media tracking stream reading error: ${error.message}`);
                observer.error(error);
              }
            }
          };

          readStream();
        }).catch(error => {
          clearTimeout(timeoutId);
          this.logger.error(`Media tracking fetch error: ${error.message}`);
          observer.error(error);
        });

        // Cleanup function
        return () => {
          clearTimeout(timeoutId);
          controller.abort();
        };
      });
    } catch (error) {
      this.logger.error(`Error starting media tracking: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thông tin API key từ RAG API
   * @param apiKey API key cụ thể (nếu không truyền sẽ dùng default)
   * @returns Thông tin API key bao gồm user_id và role
   */
  async getApiKeyInfo(apiKey?: string): Promise<{user_id: number, role: string}> {
    try {
      const keyToUse = apiKey || this.apiKey;
      this.logger.log(`Lấy thông tin API key từ RAG API: ${keyToUse.substring(0, 10)}...`);

      const endpoint = 'api/auth/keys/info';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const response = await fetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Connection': 'close',
        },
        body: JSON.stringify({ api_key: keyToUse })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.logger.log(`RAG API key info response: ${JSON.stringify(result)}`);

      // Extract user_id and role from api_metadata
      const user_id = result.api_metadata?.user_id;
      const role = result.api_metadata?.role;

      if (!user_id) {
        throw new Error('API key info does not contain user_id in api_metadata');
      }

      this.logger.log(`Đã lấy thông tin API key từ RAG API cho user_id: ${user_id}, role: ${role}`);

      return { user_id, role };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin API key: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
        `Lỗi khi lấy thông tin API key: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách vector stores từ RAG API với API key cụ thể
   * @param params Query parameters
   * @param apiKey API key cụ thể của user
   * @returns Danh sách vector stores từ RAG API
   */
  async getVectorStoresWithApiKey(params: {
    skip?: number;
    limit?: number;
    sort_by?: string;
    sort_order?: string;
    search?: string;
  }, apiKey: string): Promise<{
    total: number;
    vector_stores: Array<{
      created_at: number;
      id: string;
      name: string;
      storage: number;
      update_at: number;
    }>;
  }> {
    try {
      this.logger.log(`Lấy danh sách vector stores từ RAG API với API key: ${apiKey.substring(0, 10)}...`);

      const endpoint = 'api/v1/user/vector-stores/';

      const queryParams = new URLSearchParams();
      if (params.skip !== undefined) queryParams.append('skip', params.skip.toString());
      if (params.limit !== undefined) queryParams.append('limit', params.limit.toString());
      if (params.sort_by) queryParams.append('sort_by', params.sort_by);
      if (params.sort_order) queryParams.append('sort_order', params.sort_order);
      if (params.search) queryParams.append('search', params.search);

      const finalUrl = queryParams.toString() ? `${this.baseUrl}${endpoint}?${queryParams.toString()}` : `${this.baseUrl}${endpoint}`;

      this.logger.log(`Final URL: ${finalUrl}`);

      const response = await fetch(finalUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey, // Sử dụng API key của user
          'Connection': 'close',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.logger.log(`Đã lấy ${result.total || result.vector_stores?.length || 0} vector stores từ RAG API`);

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách vector stores: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
        `Lỗi khi lấy danh sách vector stores: ${error.message}`,
      );
    }
  }

  /**
   * Tạo nhiều vector stores cùng lúc với API key cụ thể
   */
  async createBulkVectorStoresWithApiKey(
    vectorStores: Array<{ name: string }>,
    apiKey: string
  ): Promise<{
    created_count: number;
    vector_stores: Array<{
      id: string;
      name: string;
      storage: number;
      created_at: number;
      update_at: number;
    }>;
    message?: string;
    success?: boolean;
  }> {
    try {
      this.logger.log(`Tạo ${vectorStores.length} vector stores với API key: ${apiKey.substring(0, 10)}...`);

      const endpoint = 'api/v1/user/vector-stores/bulk';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const response = await fetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey,
          'Connection': 'close',
        },
        body: JSON.stringify({ vector_stores: vectorStores }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.logger.log(`Đã tạo ${result.created_count} vector stores trên RAG API`);

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo bulk vector stores: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
        `Lỗi khi tạo bulk vector stores: ${error.message}`,
      );
    }
  }

  /**
   * Tạo nhiều vector stores cùng lúc (fire-and-forget) với API key cụ thể
   */
  async createBulkVectorStoresFireAndForgetWithApiKey(
    vectorStores: Array<{ name: string }>,
    apiKey: string
  ): Promise<void> {
    try {
      this.logger.log(`Tạo ${vectorStores.length} vector stores (fire-and-forget) với API key: ${apiKey.substring(0, 10)}...`);

      const endpoint = 'api/v1/user/vector-stores/bulk/sync';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const response = await fetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey,
          'Connection': 'close',
        },
        body: JSON.stringify({ vector_stores: vectorStores }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      this.logger.log(`Đã gửi request tạo ${vectorStores.length} vector stores đến RAG API (fire-and-forget)`);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo bulk vector stores (fire-and-forget): ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
        `Lỗi khi tạo bulk vector stores (fire-and-forget): ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều vector stores với API key cụ thể
   */
  async deleteBulkVectorStoresWithApiKey(
    vectorStoreIds: string[],
    apiKey: string
  ): Promise<{ created_count: number }> {
    try {
      this.logger.log(`Xóa ${vectorStoreIds.length} vector stores với API key: ${apiKey.substring(0, 10)}...`);

      const endpoint = 'api/v1/user/vector-stores/batch';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const response = await fetch(fullUrl, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey,
          'Connection': 'close',
        },
        body: JSON.stringify({ vector_store_ids: vectorStoreIds }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.logger.log(`Đã xóa ${result.created_count} vector stores trên RAG API`);

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa bulk vector stores: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DELETE_ERROR,
        `Lỗi khi xóa bulk vector stores: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều vector stores (fire-and-forget) với API key cụ thể
   */
  async deleteBulkVectorStoresFireAndForgetWithApiKey(
    vectorStoreIds: string[],
    apiKey: string
  ): Promise<void> {
    try {
      this.logger.log(`Xóa ${vectorStoreIds.length} vector stores (fire-and-forget) với API key: ${apiKey.substring(0, 10)}...`);

      const endpoint = 'api/v1/user/vector-stores/batch/sync';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const response = await fetch(fullUrl, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey,
          'Connection': 'close',
        },
        body: JSON.stringify({ vector_store_ids: vectorStoreIds }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      this.logger.log(`Đã gửi request xóa ${vectorStoreIds.length} vector stores đến RAG API (fire-and-forget)`);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa bulk vector stores (fire-and-forget): ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DELETE_ERROR,
        `Lỗi khi xóa bulk vector stores (fire-and-forget): ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật vector store với API key cụ thể
   */
  async updateVectorStoreWithApiKey(
    vectorStoreId: string,
    updateData: { name: string },
    apiKey: string
  ): Promise<{ id: string; name: string; storage: number; created_at: number; update_at: number }> {
    try {
      this.logger.log(`Cập nhật vector store ${vectorStoreId} với API key: ${apiKey.substring(0, 10)}...`);

      const endpoint = `api/v1/user/vector-stores/${vectorStoreId}`;
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const response = await fetch(fullUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey,
          'Connection': 'close',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.logger.log(`Đã cập nhật vector store ${vectorStoreId} trên RAG API`);

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật vector store: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_UPDATE_ERROR,
        `Lỗi khi cập nhật vector store: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết vector store với API key cụ thể
   */
  async getVectorStoreDetailWithApiKey(
    vectorStoreId: string,
    apiKey: string
  ): Promise<{
    created_at: number;
    id: string;
    name: string;
    storage: number;
    update_at: number;
  }> {
    try {
      this.logger.log(`Lấy thông tin chi tiết vector store ${vectorStoreId} với API key: ${apiKey.substring(0, 10)}...`);

      // Sử dụng endpoint đúng của RAG API
      const endpoint = `api/v1/user/vector-stores/${vectorStoreId}`;
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey,
          'Connection': 'close',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.logger.log(`Đã lấy thông tin chi tiết vector store ${vectorStoreId} từ RAG API`);

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin chi tiết vector store: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DETAIL_ERROR,
        `Lỗi khi lấy thông tin chi tiết vector store: ${error.message}`,
      );
    }
  }

  /**
   * Xử lý file từ S3 key với API key cụ thể
   */
  async processFileFromS3KeyWithApiKey(
    storageKey: string,
    chunkSize: number,
    chunkOverlap: number,
    vectorStoreId: string,
    apiKey: string
  ): Promise<{ file_id: string; status: string }> {
    try {
      this.logger.log(`Xử lý file từ S3 key ${storageKey} với API key: ${apiKey.substring(0, 10)}...`);

      const endpoint = `api/v1/user/vector-stores/${vectorStoreId}/files`;
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const response = await fetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey,
          'Connection': 'close',
        },
        body: JSON.stringify({
          storage_key: storageKey,
          chunk_size: chunkSize,
          chunk_overlap: chunkOverlap,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.logger.log(`Đã xử lý file từ S3 key ${storageKey}, file_id: ${result.file_id}`);

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý file từ S3 key: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR,
        `Lỗi khi xử lý file từ S3 key: ${error.message}`,
      );
    }
  }
}