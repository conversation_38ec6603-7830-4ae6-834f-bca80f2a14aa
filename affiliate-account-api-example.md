# API getAccount - <PERSON><PERSON> dụ Response

## Endpoint
```
GET /user/affiliate/account
```

## Headers
```
Authorization: Bearer <JWT_TOKEN>
```

## Response Example

### Thành công (200)
```json
{
  "success": true,
  "message": "<PERSON><PERSON><PERSON> thông tin tài khoản affiliate thành công",
  "result": {
    "accountInfo": {
      "id": 123,
      "partnerName": "Nguyễn Văn A",
      "accountType": "PERSONAL",
      "status": "ACTIVE",
      "createdAt": **********
    },
    "rankInfo": {
      "id": 2,
      "rankName": "Silver",
      "rankBadge": "silver_badge.png",
      "commission": 5.5,
      "minCondition": 10,
      "maxCondition": 49
    },
    "availableBalance": 1500000,
    "processingAmount": 500000,
    "referralCode": "NGUYENA123",
    "referralLink": "https://redai.vn/ref/NGUYENA123"
  }
}
```

## G<PERSON><PERSON><PERSON> thích các field mới

### `availableBalance` (number)
- **Mô tả**: <PERSON><PERSON> dư khả dụng trong tài khoản affiliate
- **Nguồn**: Lấy từ field `available_balance` trong bảng `affiliate_accounts`
- **Ví dụ**: `1500000` (1,500,000 VND)

### `processingAmount` (number)
- **Mô tả**: Tổng số tiền đang trong quá trình xử lý rút tiền
- **Nguồn**: Tính tổng `amount` từ bảng `affiliate_withdraw_history` với điều kiện:
  - `affiliate_account_id` = ID tài khoản hiện tại
  - `status` IN ('PENDING', 'INVOICE_NOT_UPLOADED')
- **Ví dụ**: `500000` (500,000 VND)

## Các trạng thái rút tiền được tính vào processingAmount

1. **PENDING**: Yêu cầu rút tiền đang chờ xử lý
2. **INVOICE_NOT_UPLOADED**: Yêu cầu rút tiền cho tài khoản doanh nghiệp chưa upload hóa đơn

## Các trạng thái KHÔNG được tính vào processingAmount

1. **APPROVED**: Đã được phê duyệt và thanh toán
2. **REJECTED**: Đã bị từ chối
3. **PAID**: Đã thanh toán xong

## Cách tính

```sql
SELECT SUM(amount) as processingAmount
FROM affiliate_withdraw_history 
WHERE affiliate_account_id = :affiliateAccountId 
  AND status IN ('PENDING', 'INVOICE_NOT_UPLOADED')
```

## Lưu ý

- Nếu không có yêu cầu rút tiền nào đang xử lý, `processingAmount` sẽ trả về `0`
- `availableBalance` và `processingAmount` đều được trả về dưới dạng số nguyên (VND)
- API này yêu cầu authentication với JWT token của user
