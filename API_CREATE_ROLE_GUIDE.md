# API Tạo Vai Trò Nhân Viên

## Tổng quan
API này cho phép tạo vai trò mới cho nhân viên trong hệ thống RedAI.

## Endpoint
```
POST /employee/roles
```

## Authentication
- <PERSON><PERSON><PERSON> cầu JWT token của nhân viên
- Header: `Authorization: Bearer <token>`

## Request Body
```json
{
  "id": number,           // ID của vai trò (bắt buộc)
  "name": string,         // Tên vai trò (bắt buộc)
  "description": string,  // Mô tả vai trò (bắt buộc)
  "permissionIds": number[] // Danh sách ID quyền (tùy chọn)
}
```

## Ví dụ Request
```json
{
  "id": 10,
  "name": "Quản lý nội dung",
  "description": "Vai trò quản lý nội dung blog và bài viết",
  "permissionIds": [1, 2, 3, 5]
}
```

## Response

### Thành công (201)
```json
{
  "success": true,
  "message": "Tạo vai trò thành công",
  "data": {
    "id": 10,
    "name": "Quản lý nội dung",
    "description": "Vai trò quản lý nội dung blog và bài viết",
    "permissions": [
      {
        "id": 1,
        "module": "blogs",
        "action": "read",
        "description": "Xem danh sách blog"
      },
      // ... các quyền khác
    ]
  }
}
```

### Lỗi (400/409/500)
```json
{
  "success": false,
  "message": "Thông báo lỗi",
  "error": {
    "code": 15021,
    "message": "Chi tiết lỗi"
  }
}
```

## Các trường hợp lỗi

1. **ID đã tồn tại (409)**
   - Message: "Vai trò với ID 'X' đã tồn tại"

2. **Tên đã tồn tại (409)**
   - Message: "Vai trò với tên 'X' đã tồn tại"

3. **Quyền không tồn tại (400)**
   - Message: "Không tìm thấy quyền với ID: X, Y, Z"

4. **Dữ liệu không hợp lệ (400)**
   - Thiếu trường bắt buộc
   - Kiểu dữ liệu không đúng

## Validation Rules

- `id`: Số nguyên, bắt buộc, duy nhất
- `name`: Chuỗi, bắt buộc, duy nhất, không rỗng
- `description`: Chuỗi, bắt buộc, không rỗng
- `permissionIds`: Mảng số nguyên, tùy chọn, các ID phải tồn tại

## Swagger Documentation
API này được tài liệu hóa đầy đủ trong Swagger UI tại:
```
http://localhost:3000/api-docs
```

Tìm kiếm tag "Employee - Roles" để xem chi tiết.

## Testing
Sử dụng file `test-create-role-api.http` để test API với các trường hợp khác nhau.
