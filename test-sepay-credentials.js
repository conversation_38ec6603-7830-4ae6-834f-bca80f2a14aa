require('dotenv').config();

console.log('🔍 Checking SepayHub Environment Variables:\n');

const requiredVars = [
  'SEPAY_HUB_API_URL',
  'SEPAY_HUB_API_KEY', 
  'SEPAY_HUB_SECRET_KEY'
];

let allConfigured = true;

requiredVars.forEach(varName => {
  const value = process.env[varName];
  const isConfigured = value && value.trim() !== '';
  
  console.log(`${varName}: ${isConfigured ? '✅ SET' : '❌ NOT SET'}`);
  
  if (isConfigured) {
    console.log(`  Length: ${value.length} characters`);
    console.log(`  Preview: ${value.substring(0, 8)}...`);
  }
  
  if (!isConfigured) {
    allConfigured = false;
  }
  
  console.log('');
});

console.log(`Overall Status: ${allConfigured ? '✅ All configured' : '❌ Missing configuration'}`);

if (allConfigured) {
  // Test Basic Auth encoding
  const apiKey = process.env.SEPAY_HUB_API_KEY;
  const secretKey = process.env.SEPAY_HUB_SECRET_KEY;
  const credentials = Buffer.from(`${apiKey}:${secretKey}`).toString('base64');
  
  console.log('\n🔐 Basic Auth Test:');
  console.log(`Credentials: ${credentials.substring(0, 20)}...`);
  console.log(`Full length: ${credentials.length} characters`);
}
