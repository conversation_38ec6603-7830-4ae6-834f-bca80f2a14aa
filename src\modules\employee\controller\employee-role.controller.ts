import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { ApiResponseDto as AppApiResponse } from '@common/response/api-response-dto';
import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AssignRolePermissionsDto } from '../dto/assign-role-permissions.dto';
import { CreateEmployeeRoleDto } from '../dto/create-employee-role.dto';
import { EmployeeRole, Permission } from '../entities';
import { EmployeeRoleService } from '../services/employee-role.service';

@ApiTags(SWAGGER_API_TAGS.EMPLOYEE_ROLES)
@Controller('employee/roles')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
export class EmployeeRoleController {
  constructor(private readonly employeeRoleService: EmployeeRoleService) { }

  @Post()
  @ApiOperation({ summary: 'Tạo vai trò mới' })
  @ApiResponse({
    status: 201,
    description: 'Vai trò đã được tạo thành công',
    type: EmployeeRole
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 409, description: 'Vai trò đã tồn tại' })
  @ApiResponse({ status: 500, description: 'Lỗi server khi tạo vai trò' })
  async createRole(@Body() createDto: CreateEmployeeRoleDto): Promise<AppApiResponse<EmployeeRole>> {
    const role = await this.employeeRoleService.createRole(createDto);
    return AppApiResponse.success(role, 'Tạo vai trò thành công');
  }

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tất cả vai trò' })
  @ApiResponse({ status: 200, description: 'Danh sách vai trò' })
  async getAllRoles(): Promise<AppApiResponse<EmployeeRole[]>> {
    const roles = await this.employeeRoleService.getAllRoles();
    return AppApiResponse.success(roles);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin vai trò theo ID' })
  @ApiParam({ name: 'id', description: 'ID của vai trò' })
  @ApiResponse({ status: 200, description: 'Thông tin vai trò' })
  @ApiResponse({ status: 404, description: 'Vai trò không tồn tại' })
  async getRoleById(@Param('id') id: number): Promise<AppApiResponse<EmployeeRole>> {
    const role = await this.employeeRoleService.getRoleById(id);
    return AppApiResponse.success(role);
  }

  @Get('permissions/all')
  @ApiOperation({ summary: 'Lấy danh sách tất cả quyền' })
  @ApiResponse({ status: 200, description: 'Danh sách quyền' })
  async getAllPermissions(): Promise<AppApiResponse<Permission[]>> {
    const permissions = await this.employeeRoleService.getAllPermissions();
    return AppApiResponse.success(permissions);
  }

  @Get(':id/permissions')
  @ApiOperation({ summary: 'Lấy danh sách quyền của vai trò' })
  @ApiParam({ name: 'id', description: 'ID của vai trò' })
  @ApiResponse({ status: 200, description: 'Danh sách quyền của vai trò' })
  @ApiResponse({ status: 404, description: 'Vai trò không tồn tại' })
  async getRolePermissions(@Param('id') id: number): Promise<AppApiResponse<Permission[]>> {
    const permissions = await this.employeeRoleService.getRolePermissions(id);
    return AppApiResponse.success(permissions);
  }

  @Post(':id/permissions')
  @ApiOperation({ summary: 'Gán quyền cho vai trò' })
  @ApiParam({ name: 'id', description: 'ID của vai trò' })
  @ApiResponse({ status: 200, description: 'Vai trò đã được cập nhật với các quyền mới' })
  @ApiResponse({ status: 404, description: 'Vai trò hoặc quyền không tồn tại' })
  async assignPermissionsToRole(
    @Param('id') id: number,
    @Body() dto: AssignRolePermissionsDto,
  ): Promise<AppApiResponse<EmployeeRole>> {
    const role = await this.employeeRoleService.assignPermissionsToRole(id, dto);
    return AppApiResponse.success(role);
  }
}
