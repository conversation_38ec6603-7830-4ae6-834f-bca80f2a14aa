import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsBoolean, IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto';
import { ProviderEnum } from '../../../constants';

export enum SystemModelsSortBy {
  // Full format với prefix
  MODEL_ID_FULL = 'systemModels.modelId',
  ACTIVE_FULL = 'systemModels.active',

  // Short format không prefix
  MODEL_ID = 'modelId',
  ACTIVE = 'active',
}

/**
 * DTO cho việc truy vấn danh sách system models
 */
export class SystemModelsQueryDto extends QueryDto {
  /**
   * Lọc theo nhà cung cấp (bắt buộc)
   */
  @ApiProperty({
    description: 'Lọc theo nhà cung cấp (bắt buộc)',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
    required: true,
  })
  @IsNotEmpty({ message: 'Provider là bắt buộc' })
  @IsEnum(ProviderEnum, { message: 'Provider phải là một trong các giá trị hợp lệ' })
  provider: ProviderEnum;

  /**
   * Lọc theo trạng thái hoạt động
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái hoạt động',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  active?: boolean;

  @ApiPropertyOptional({
    description: 'Trường sắp xếp',
    enum: SystemModelsSortBy,
    default: SystemModelsSortBy.MODEL_ID
  })
  @IsOptional()
  @IsEnum(SystemModelsSortBy)
  sortBy?: SystemModelsSortBy = SystemModelsSortBy.MODEL_ID;

  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.ASC,
  })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortDirection?: SortDirection = SortDirection.ASC;
}


