### Test Bulk Delete Affiliate Rank API

# Xóa nhiều rank affiliate
DELETE {{baseUrl}}/admin/affiliate/ranks/bulk-delete
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "ids": [1, 2, 3]
}

### Test với danh sách ID rỗng (sẽ lỗi validation)
DELETE {{baseUrl}}/admin/affiliate/ranks/bulk-delete
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "ids": []
}

### Test với quá nhiều ID (sẽ lỗi validation)
DELETE {{baseUrl}}/admin/affiliate/ranks/bulk-delete
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "ids": [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51]
}

### Test với ID không hợp lệ
DELETE {{baseUrl}}/admin/affiliate/ranks/bulk-delete
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "ids": ["abc", null, undefined]
}

### Variables
@baseUrl = http://localhost:3000/api/v1
@adminToken = your_admin_jwt_token_here
