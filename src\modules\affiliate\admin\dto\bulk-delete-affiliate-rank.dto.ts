import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNumber, ArrayMinSize, ArrayMaxSize } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc xóa nhiều rank affiliate
 */
export class BulkDeleteAffiliateRankDto {
  @ApiProperty({
    description: 'Danh sách ID của các rank affiliate cần xóa',
    example: [1, 2, 3],
    type: [Number],
    minItems: 1,
    maxItems: 50
  })
  @IsArray({ message: 'Danh sách ID phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 ID để xóa' })
  @ArrayMaxSize(50, { message: 'Không thể xóa quá 50 rank cùng lúc' })
  @IsNumber({}, { each: true, message: 'Mỗi ID phải là một số' })
  @Type(() => Number)
  ids: number[];
}

/**
 * DTO cho kết quả xóa nhiều rank affiliate
 */
export class BulkDeleteAffiliateRankResponseDto {
  @ApiProperty({
    description: 'Số lượng rank đã xóa thành công',
    example: 2
  })
  successCount: number;

  @ApiProperty({
    description: 'Số lượng rank xóa thất bại',
    example: 1
  })
  failureCount: number;

  @ApiProperty({
    description: 'Tổng số rank được yêu cầu xóa',
    example: 3
  })
  totalCount: number;

  @ApiProperty({
    description: 'Danh sách ID đã xóa thành công',
    example: [1, 2],
    type: [Number]
  })
  successIds: number[];

  @ApiProperty({
    description: 'Danh sách ID xóa thất bại với lý do',
    example: [
      {
        id: 3,
        reason: 'Rank không tồn tại'
      }
    ],
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: {
          type: 'number',
          description: 'ID của rank xóa thất bại'
        },
        reason: {
          type: 'string',
          description: 'Lý do xóa thất bại'
        }
      }
    }
  })
  failures: Array<{
    id: number;
    reason: string;
  }>;

  @ApiProperty({
    description: 'Thông báo tổng quan về kết quả',
    example: 'Đã xóa thành công 2/3 rank affiliate'
  })
  message: string;
}
