# Test Webhook Redis Update

## M<PERSON> tả vấn đề
Khi nhận webhook từ SePay, hệ thống cập nhật trạng thái giao dịch trong database nhưng chưa cập nhật trạng thái trong Redis. <PERSON><PERSON><PERSON><PERSON> này khiến frontend gọi API `checkTransactionStatus` không thấy trạng thái `CONFIRMED`.

## Giải pháp đã thực hiện
1. **Inject TransactionRedisService** vào `WebhookUserService`
2. **Cập nhật Redis** sau khi cập nhật database trong webhook handler

## Thay đổi code

### 1. WebhookUserService - Inject TransactionRedisService
```typescript
// src/modules/r-point/user/services/webhook-user.service.ts
import { TransactionRedisService } from './transaction-redis.service';

@Injectable()
export class WebhookUserService {
  constructor(
    private readonly configService: ConfigService,
    private readonly transactionRepository: PointPurchaseTransactionRepository,
    private readonly transactionRedisService: TransactionRedisService, // ← Thêm inject
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {
    this.webhookApiKey = this.configService.get<string>('SEPAY_WEBHOOK_API_KEY');
  }
```

### 2. Cập nhật Redis trong processWebhook
```typescript
// Cập nhật trạng thái giao dịch
await this.updateTransaction(transaction, webhookRequest);

// Cập nhật trạng thái trong Redis ← Thêm dòng này
await this.transactionRedisService.updateTransactionStatus(
  transaction.id,
  TransactionStatus.CONFIRMED
);

// Cộng điểm cho người dùng
await this.addPointsToUser(user, transaction.pointsAmount);
```

## Luồng hoạt động sau khi fix

### 1. Tạo giao dịch mua R-Point
```
POST /user/r-point/payment/purchase
```
- Tạo giao dịch với status `PENDING`
- Lưu vào Redis với status `PENDING`
- Trả về QR code cho user

### 2. User thanh toán qua ngân hàng
- User chuyển khoản với nội dung `REDAI{transactionId}SEPAY`
- SePay gửi webhook đến hệ thống

### 3. Webhook xử lý thanh toán
```
POST /r-point/webhook/ipn
```
- Cập nhật database: status = `CONFIRMED`
- **Cập nhật Redis: status = `CONFIRMED`** ← Fix này
- Cộng R-Point cho user

### 4. Frontend kiểm tra trạng thái
```
GET /user/r-point/payment/status/{transactionId}
```
- Kiểm tra Redis trước
- Nếu có trong Redis và status = `CONFIRMED` → Trả về ngay
- Nếu không có trong Redis → Tìm trong database và lưu vào Redis

## Test Case

### Test 1: Webhook cập nhật Redis thành công
```bash
# 1. Tạo giao dịch
curl -X POST "http://localhost:3000/user/r-point/payment/purchase" \
  -H "Authorization: Bearer {JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "pointAmount": 100,
    "pointId": 1
  }'

# Response: transactionId = 123

# 2. Kiểm tra Redis trước webhook (status = PENDING)
curl -X GET "http://localhost:3000/user/r-point/payment/status/123" \
  -H "Authorization: Bearer {JWT_TOKEN}"

# Response: status = "PENDING"

# 3. Gửi webhook giả lập
curl -X POST "http://localhost:3000/r-point/webhook/ipn" \
  -H "Authorization: Apikey {SEPAY_API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "transferType": "in",
    "transferAmount": 100000,
    "content": "REDAI123SEPAY",
    "referenceCode": "REF123456",
    "transactionDate": "2024-01-15 10:30:00"
  }'

# Response: success = true

# 4. Kiểm tra Redis sau webhook (status = CONFIRMED)
curl -X GET "http://localhost:3000/user/r-point/payment/status/123" \
  -H "Authorization: Bearer {JWT_TOKEN}"

# Expected Response: status = "CONFIRMED"
```

### Test 2: Kiểm tra log Redis update
```bash
# Kiểm tra log để đảm bảo Redis được cập nhật
tail -f logs/application.log | grep "Đã cập nhật trạng thái giao dịch"
```

## Kết quả mong đợi

### Trước fix:
1. Webhook cập nhật database ✅
2. Webhook cập nhật Redis ❌
3. Frontend gọi checkTransactionStatus → status vẫn là `PENDING`

### Sau fix:
1. Webhook cập nhật database ✅
2. Webhook cập nhật Redis ✅
3. Frontend gọi checkTransactionStatus → status là `CONFIRMED`

## Lưu ý
- Redis có TTL 30 phút, sau đó dữ liệu sẽ bị xóa
- Nếu Redis không có dữ liệu, API sẽ fallback về database
- Cần đảm bảo `TransactionRedisService` được inject đúng trong module
