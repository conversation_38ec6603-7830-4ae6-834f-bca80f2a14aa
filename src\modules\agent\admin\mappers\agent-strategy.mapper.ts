import { Agent } from '@modules/agent/entities/agent.entity';
import { AgentStrategy } from '@modules/agent/entities/agents-strategy.entity';
import { AgentStrategyResponseDto, AgentStrategyDetailResponseDto } from '@modules/agent/admin/dto/agent-strategy';
import { TimeIntervalEnum } from '@/shared/utils';
import { CdnService } from '@/shared/services/cdn.service';

/**
 * Mapper cho AgentStrategy
 * Chuyển đổi giữa entity và DTO
 */
export class AgentStrategyMapper {
  /**
   * Chuyển đổi từ Agent và AgentStrategy entities sang AgentStrategyResponseDto
   * @param agent Agent entity
   * @param strategy AgentStrategy entity
   * @param cdnService CDN service để tạo URL
   * @param modelId Model ID
   * @returns AgentStrategyResponseDto
   */
  static toResponseDto(agent: Agent, strategy: AgentStrategy, cdnService: CdnService, modelId?: string): AgentStrategyResponseDto {
    return {
      id: agent.id,
      name: agent.name,
      avatar: agent.avatar ? cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY) : null,
      modelId: modelId || '',
      createdAt: agent.createdAt,
      updatedAt: agent.updatedAt,
      activeDelete: !strategy.using,
    };
  }

  /**
   * Chuyển đổi từ Agent và AgentStrategy entities sang AgentStrategyDetailResponseDto
   * @param agent Agent entity
   * @param strategy AgentStrategy entity
   * @param cdnService CDN service để tạo URL
   * @param modelId Model ID
   * @param modelSystemId System Model ID
   * @param createdByEmployee Thông tin employee tạo
   * @param updatedByEmployee Thông tin employee cập nhật
   * @returns AgentStrategyDetailResponseDto
   */
  static toDetailResponseDto(
    agent: Agent,
    strategy: AgentStrategy,
    cdnService: CdnService,
    modelId?: string,
    modelSystemId?: string,
    createdByEmployee?: { employeeId: number; name: string; avatar: string | null },
    updatedByEmployee?: { employeeId: number; name: string; avatar: string | null }
  ): AgentStrategyDetailResponseDto {
    return {
      // Thông tin cơ bản từ AgentStrategyResponseDto
      id: agent.id,
      name: agent.name,
      avatar: agent.avatar ? cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY) : null,
      modelId: modelId || '',
      createdAt: agent.createdAt,
      updatedAt: agent.updatedAt,
      activeDelete: !strategy.using,

      // Thông tin chi tiết bổ sung
      modelConfig: agent.modelConfig,
      instruction: agent.instruction || undefined,
      vectorStoreId: agent.vectorStoreId || undefined,
      modelSystemId: modelSystemId || '',

      // Thông tin từ AgentStrategy
      content: strategy.content || [],
      exampleDefault: strategy.exampleDefault || [],
      createdBy: createdByEmployee ? {
        employeeId: createdByEmployee.employeeId,
        name: createdByEmployee.name,
        avatar: createdByEmployee.avatar ? cdnService.generateUrlView(createdByEmployee.avatar, TimeIntervalEnum.ONE_DAY) : null
      } : undefined,
      updatedBy: updatedByEmployee ? {
        employeeId: updatedByEmployee.employeeId,
        name: updatedByEmployee.name,
        avatar: updatedByEmployee.avatar ? cdnService.generateUrlView(updatedByEmployee.avatar, TimeIntervalEnum.ONE_DAY) : null
      } : undefined,
    };
  }

  /**
   * Chuyển đổi từ mảng combined entities sang mảng AgentStrategyResponseDto
   * @param combinedEntities Mảng object chứa agent và strategy
   * @param cdnService CDN service để tạo URL
   * @returns Mảng AgentStrategyResponseDto
   */
  static toResponseDtoArray(combinedEntities: { agent: Agent; strategy: AgentStrategy; modelId?: string }[], cdnService: CdnService): AgentStrategyResponseDto[] {
    return combinedEntities.map(({ agent, strategy, modelId }) => this.toResponseDto(agent, strategy, cdnService, modelId));
  }
}
