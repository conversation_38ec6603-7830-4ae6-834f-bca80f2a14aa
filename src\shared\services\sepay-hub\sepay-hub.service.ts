import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AxiosRequestConfig, AxiosError } from 'axios';
import { AppException, ErrorCode } from '@/common';
import {
  AccessTokenDto,
  AccountLookupRequestDto,
  BankAccountConfirmRequestDto,
  BankAccountCreateRequestDto,
  BankAccountCreateResponseDto,
  BankAccountDto,
  BankDto,
  CompanyConfigDto,
  CompanyCreateRequestDto,
  CompanyDto,
  CompanyStatus,
  CompanyUpdateRequestDto,
  CreateVARequestDto,
  EditBankAccountRequestDto,
  OtpRequestDto,
  PaginationMetaDto,
  TransactionCounterDto,
  TransactionHistoryDto,
  UpdateCompanyConfigRequestDto,
  VirtualAccountDto,
} from '@/shared/interface/sepay-hub';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service để tương tác với SePay Hub API
 */
@Injectable()
export class SepayHubService {
  private readonly logger = new Logger(SepayHubService.name);
  private readonly baseUrl: string;
  private readonly clientId: string;
  private readonly clientSecret: string;
  private accessToken: string | null;
  private tokenExpiry: number;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get<string>('SEPAY_HUB_API_URL', 'https://partner-api.sepay.vn/merchant/v1');
    this.clientId = this.configService.get<string>('SEPAY_HUB_CLIENT_ID', '');
    this.clientSecret = this.configService.get<string>('SEPAY_HUB_CLIENT_SECRET', '');
    this.accessToken = null;
    this.tokenExpiry = 0;

    // Validate credentials
    if (!this.clientId || !this.clientSecret) {
      this.logger.warn('SepayHub credentials not configured properly');
      this.logger.warn(`Client ID configured: ${!!this.clientId}`);
      this.logger.warn(`Client Secret configured: ${!!this.clientSecret}`);
    }
  }

  /**
   * Tạo UUID cho Client-Message-Id
   * @returns UUID v4
   */
  private generateClientMessageId(): string {
    return uuidv4();
  }

  /**
   * Kiểm tra credentials có hợp lệ không
   * @returns true nếu credentials hợp lệ
   */
  private validateCredentials(): boolean {
    if (!this.clientId || !this.clientSecret) {
      this.logger.error('SepayHub credentials missing');
      return false;
    }

    if (this.clientId.trim() === '' || this.clientSecret.trim() === '') {
      this.logger.error('SepayHub credentials empty');
      return false;
    }

    return true;
  }

  /**
   * Tạo header Authorization với Basic Auth
   * @returns Header Authorization
   */
  private getBasicAuthHeader(): string {
    if (!this.validateCredentials()) {
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'SepayHub credentials not configured properly'
      );
    }

    const credentials = Buffer.from(`${this.clientId}:${this.clientSecret}`).toString('base64');
    return `Basic ${credentials}`;
  }

  /**
   * Tạo header Authorization với Bearer Token
   * @returns Header Authorization
   */
  private async getBearerAuthHeader(): Promise<string> {
    // Kiểm tra xem token đã hết hạn chưa
    const now = Math.floor(Date.now() / 1000);
    if (!this.accessToken || now >= this.tokenExpiry) {
      await this.refreshAccessToken();
    }
    return `Bearer ${this.accessToken}`;
  }

  /**
   * Làm mới access token
   */
  private async refreshAccessToken(): Promise<void> {
    try {
      const url = `${this.baseUrl}/token/create`;
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': this.getBasicAuthHeader(),
      };

      // Log thông tin debug (ẩn sensitive data)
      this.logger.debug(`Refreshing access token from: ${url}`);
      this.logger.debug(`Client ID exists: ${!!this.clientId}`);
      this.logger.debug(`Client Secret exists: ${!!this.clientSecret}`);
      this.logger.debug(`Client ID length: ${this.clientId?.length || 0}`);
      this.logger.debug(`Client Secret length: ${this.clientSecret?.length || 0}`);

      const response = await lastValueFrom(
        this.httpService.post<AccessTokenDto>(url, {}, { headers })
      );

      if (response.data && response.data.data && response.data.data.access_token) {
        this.accessToken = response.data.data.access_token;
        // Tính thời gian hết hạn (trừ 60 giây để đảm bảo an toàn)
        const now = Math.floor(Date.now() / 1000);
        this.tokenExpiry = now + (response.data.data.ttl - 60);
      } else {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Phản hồi token không hợp lệ từ SePay Hub API'
        );
      }
    } catch (error) {
      // Log chi tiết lỗi trước khi handle
      if (error instanceof AxiosError) {
        this.logger.error(`Refresh token failed - Status: ${error.response?.status}`);
        this.logger.error(`Refresh token failed - Response: ${JSON.stringify(error.response?.data)}`);
        this.logger.error(`Refresh token failed - URL: ${error.config?.url}`);
        this.logger.error(`Refresh token failed - Headers: ${JSON.stringify(error.config?.headers)}`);
      }

      this.handleApiError(error, 'làm mới access token');
    }
  }

  /**
   * Tạo config cho request API
   * @returns AxiosRequestConfig
   */
  private async createRequestConfig(): Promise<AxiosRequestConfig> {
    const clientMessageId = this.generateClientMessageId();
    const authorization = await this.getBearerAuthHeader();

    return {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authorization,
        'Client-Message-Id': clientMessageId,
        'Request-Id': '', // Default empty value, will be overridden when needed
      },
    };
  }

  /**
   * Xử lý lỗi từ SePay Hub API một cách nhất quán
   * @param error Lỗi gốc
   * @param operation Tên thao tác đang thực hiện
   * @throws AppException
   */
  private handleApiError(error: any, operation: string): never {
    this.logger.error(`Error ${operation}: ${error.message}`, error.stack);

    if (error instanceof AppException) {
      throw error;
    }

    if (error instanceof AxiosError) {
      if (error.response) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi ${operation}: ${error.response.data?.message || error.message}`,
          { statusCode: error.response.status }
        );
      }

      if (error.request) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Không thể kết nối đến SePay Hub API khi ${operation}`,
          { originalError: error.message }
        );
      }
    }

    throw new AppException(
      ErrorCode.EXTERNAL_SERVICE_ERROR,
      `Lỗi không xác định khi ${operation}`,
      { originalError: error.message }
    );
  }

  /**
   * Test connection và credentials
   * @returns true nếu kết nối thành công
   */
  async testConnection(): Promise<boolean> {
    try {
      // Thử refresh token để test credentials
      await this.refreshAccessToken();
      this.logger.log('SepayHub connection test successful');
      return true;
    } catch (error) {
      this.logger.error(`SepayHub connection test failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Gọi API để lấy danh sách ngân hàng
   * @returns Danh sách ngân hàng
   */
  async getBanks(): Promise<BankDto[]> {
    try {
      const url = `${this.baseUrl}/bank`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.get<{ data: BankDto[] }>(url, config)
      );
      return response.data.data;
    } catch (error) {
      return this.handleApiError(error, 'lấy danh sách ngân hàng');
    }
  }

  /**
   * Gọi API để lấy bộ đếm giao dịch
   * @param date Ngày cần lấy bộ đếm (định dạng Y-m-d)
   * @returns Bộ đếm giao dịch
   */
  async getTransactionCounter(date?: string): Promise<TransactionCounterDto> {
    try {
      const url = `${this.baseUrl}/merchant`;
      const config = await this.createRequestConfig();

      if (date) {
        config.params = { date };
      }

      const response = await lastValueFrom(
        this.httpService.get<{ data: TransactionCounterDto }>(url, config)
      );
      return response.data.data;
    } catch (error) {
      return this.handleApiError(error, 'lấy bộ đếm giao dịch');
    }
  }

  /**
   * Gọi API để tạo công ty mới
   * @param request Thông tin công ty cần tạo
   * @returns Thông tin công ty đã tạo
   */
  async createCompany(request: CompanyCreateRequestDto): Promise<CompanyDto> {
    try {
      const url = `${this.baseUrl}/company/create`;
      const config = await this.createRequestConfig();

      this.logger.debug(`Creating company with request: ${JSON.stringify(request)}`);

      const response = await lastValueFrom(
        this.httpService.post<{ code: number; message: string; id: string }>(url, request, config)
      );

      this.logger.debug(`Create company response: ${JSON.stringify(response.data)}`);

      if (!response.data || !response.data.id) {
        this.logger.error(`Invalid response structure: ${JSON.stringify(response.data)}`);
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Phản hồi không hợp lệ từ SePay Hub API khi tạo công ty'
        );
      }

      // SePay Hub trả về format khác, cần tạo CompanyDto từ response
      const now = new Date();
      const timestamp = now.toISOString().slice(0, 19).replace('T', ' '); // Format: Y-m-d H:i:s

      const companyDto: CompanyDto = {
        id: response.data.id,
        full_name: request.full_name,
        short_name: request.short_name,
        status: CompanyStatus.ACTIVE, // Mặc định là ACTIVE khi tạo thành công
        created_at: timestamp,
        updated_at: timestamp,
      };

      return companyDto;
    } catch (error) {
      this.logger.error(`Error creating company: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để lấy danh sách công ty
   * @param params Tham số tìm kiếm
   * @returns Danh sách công ty
   */
  async getCompanies(params?: {
    per_page?: string;
    q?: string;
    status?: string;
    sort?: string;
  }): Promise<{ data: CompanyDto[]; meta: PaginationMetaDto }> {
    try {
      const url = `${this.baseUrl}/company`;
      const config = await this.createRequestConfig();

      if (params) {
        config.params = params;
      }

      const response = await lastValueFrom(
        this.httpService.get<{ data: CompanyDto[]; meta: PaginationMetaDto }>(url, config)
      );
      return {
        data: response.data.data,
        meta: response.data.meta,
      };
    } catch (error) {
      this.logger.error(`Error getting companies: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để lấy thông tin chi tiết của một công ty
   * @param companyId ID của công ty
   * @returns Thông tin chi tiết của công ty
   */
  async getCompanyDetails(companyId: string): Promise<CompanyDto> {
    try {
      const url = `${this.baseUrl}/company/details/${companyId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.get<{ data: CompanyDto }>(url, config)
      );
      return response.data.data;
    } catch (error) {
      this.logger.error(`Error getting company details: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để lấy cấu hình của một công ty
   * @param companyId ID của công ty
   * @returns Cấu hình của công ty
   */
  async getCompanyConfiguration(companyId: string): Promise<CompanyConfigDto> {
    try {
      const url = `${this.baseUrl}/company/configuration/${companyId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.get<{ data: CompanyConfigDto }>(url, config)
      );
      return response.data.data;
    } catch (error) {
      this.logger.error(`Error getting company configuration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để cập nhật cấu hình của một công ty
   * @param companyId ID của công ty
   * @param request Thông tin cấu hình cần cập nhật
   * @returns Kết quả cập nhật
   */
  async updateCompanyConfiguration(
    companyId: string,
    request: UpdateCompanyConfigRequestDto
  ): Promise<{ success: boolean; message: string }> {
    try {
      const url = `${this.baseUrl}/company/configuration/${companyId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<{ code: number; message: string }>(url, request, config)
      );
      return {
        success: response.data.code === 200,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error updating company configuration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để lấy bộ đếm giao dịch của một công ty
   * @param companyId ID của công ty
   * @param date Ngày cần lấy bộ đếm (định dạng Y-m-d)
   * @returns Bộ đếm giao dịch
   */
  async getCompanyCounter(
    companyId: string,
    date?: string
  ): Promise<TransactionCounterDto> {
    try {
      const url = `${this.baseUrl}/company/counter/${companyId}`;
      const config = await this.createRequestConfig();

      if (date) {
        config.params = { date };
      }

      const response = await lastValueFrom(
        this.httpService.get<{ data: TransactionCounterDto }>(url, config)
      );
      return response.data.data;
    } catch (error) {
      this.logger.error(`Error getting company counter: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để tra cứu tên chủ tài khoản MB Bank
   * @param request Thông tin tài khoản cần tra cứu
   * @returns Thông tin chủ tài khoản
   */
  async getAccountHolderNameMB(
    request: AccountLookupRequestDto
  ): Promise<{ account_holder_name: string }> {
    try {
      const url = `${this.baseUrl}/mb/individual/bankAccount/lookUpAccountHolderName`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<{ data: { account_holder_name: string } }>(url, request, config)
      );
      return response.data.data;
    } catch (error) {
      this.logger.error(`Error getting account holder name: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để tạo tài khoản ngân hàng MB Bank
   * @param request Thông tin tài khoản cần tạo
   * @returns Thông tin tài khoản đã tạo
   */
  async createBankAccountMB(
    request: BankAccountCreateRequestDto
  ): Promise<BankAccountCreateResponseDto> {
    try {
      const url = `${this.baseUrl}/mb/individual/bankAccount/create`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<BankAccountCreateResponseDto>(url, request, config)
      );
      return response.data;
    } catch (error) {
      this.logger.error(`Error creating bank account: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để xác nhận kết nối API MB Bank
   * @param requestId ID của yêu cầu
   * @param request Thông tin OTP
   * @returns Kết quả xác nhận
   */
  async confirmBankAccountConnectionMB(
    requestId: string,
    request: BankAccountConfirmRequestDto
  ): Promise<{ success: boolean; message: string }> {
    try {
      const url = `${this.baseUrl}/mb/individual/bankAccount/confirmApiConnection`;
      const config = await this.createRequestConfig();

      if (config.headers) {
        config.headers['Request-Id'] = requestId;
      }

      const response = await lastValueFrom(
        this.httpService.post<{ code: number; message: string }>(url, request, config)
      );
      return {
        success: response.data.code === 200,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error confirming bank account connection: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để yêu cầu kết nối API MB Bank
   * @param bankAccountId ID của tài khoản ngân hàng
   * @returns Kết quả yêu cầu
   */
  async requestApiConnectionMB(
    bankAccountId: string
  ): Promise<{ request_id: string; message: string }> {
    try {
      const url = `${this.baseUrl}/mb/individual/bankAccount/requestApiConnection/${bankAccountId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<{ data: { request_id: string }; message: string }>(url, {}, config)
      );
      return {
        request_id: response.data.data.request_id,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error requesting API connection: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để lấy danh sách tài khoản ngân hàng
   * @param params Tham số tìm kiếm
   * @returns Danh sách tài khoản ngân hàng
   */
  async getBankAccounts(params?: {
    per_page?: string;
    page?: string;
    q?: string;
    company_id?: string;
    bank_id?: string;
  }): Promise<{ data: BankAccountDto[]; meta: PaginationMetaDto }> {
    try {
      const url = `${this.baseUrl}/bankAccount`;
      const config = await this.createRequestConfig();

      if (params) {
        config.params = params;
      }

      const response = await lastValueFrom(
        this.httpService.get<{ data: BankAccountDto[]; meta: PaginationMetaDto }>(url, config)
      );
      return {
        data: response.data.data,
        meta: response.data.meta,
      };
    } catch (error) {
      this.logger.error(`Error getting bank accounts: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để lấy thông tin chi tiết của một tài khoản ngân hàng
   * @param bankAccountId ID của tài khoản ngân hàng
   * @returns Thông tin chi tiết của tài khoản ngân hàng
   */
  async getBankAccountDetails(bankAccountId: string): Promise<BankAccountDto> {
    try {
      const url = `${this.baseUrl}/bankAccount/details/${bankAccountId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.get<{ data: BankAccountDto }>(url, config)
      );
      return response.data.data;
    } catch (error) {
      this.logger.error(`Error getting bank account details: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để yêu cầu tạo tài khoản ảo OCB
   * @param request Thông tin tài khoản ảo cần tạo
   * @returns Kết quả yêu cầu
   */
  async requestCreateVAOCB(
    request: CreateVARequestDto
  ): Promise<{ request_id: string; message: string }> {
    try {
      const url = `${this.baseUrl}/ocb/individual/VA/requestCreate`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<{ data: { request_id: string }; message: string }>(url, request, config)
      );
      return {
        request_id: response.data.data.request_id,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error requesting VA creation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để xác nhận tạo tài khoản ảo OCB
   * @param requestId ID của yêu cầu
   * @param request Thông tin OTP
   * @returns Kết quả xác nhận
   */
  async confirmCreateVAOCB(
    requestId: string,
    request: OtpRequestDto
  ): Promise<{ va: VirtualAccountDto; message: string }> {
    try {
      const url = `${this.baseUrl}/ocb/individual/VA/confirmCreate`;
      const config = await this.createRequestConfig();

      if (config.headers) {
        config.headers['Request-Id'] = requestId;
      }

      const response = await lastValueFrom(
        this.httpService.post<{ data: { va: VirtualAccountDto }; message: string }>(url, request, config)
      );
      return {
        va: response.data.data.va,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error confirming VA creation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để lấy danh sách tài khoản ảo
   * @param params Tham số tìm kiếm
   * @returns Danh sách tài khoản ảo
   */
  async getVirtualAccounts(params?: {
    per_page?: string;
    page?: string;
    q?: string;
    company_id?: string;
    bank_account_id?: string;
  }): Promise<{ data: VirtualAccountDto[]; meta: PaginationMetaDto }> {
    try {
      const url = `${this.baseUrl}/ocb/individual/VA`;
      const config = await this.createRequestConfig();

      if (params) {
        config.params = params;
      }

      const response = await lastValueFrom(
        this.httpService.get<{ data: VirtualAccountDto[]; meta: PaginationMetaDto }>(url, config)
      );
      return {
        data: response.data.data,
        meta: response.data.meta,
      };
    } catch (error) {
      this.logger.error(`Error getting virtual accounts: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để lấy thông tin chi tiết của một tài khoản ảo
   * @param vaId ID của tài khoản ảo
   * @returns Thông tin chi tiết của tài khoản ảo
   */
  async getVirtualAccountDetails(vaId: string): Promise<VirtualAccountDto> {
    try {
      const url = `${this.baseUrl}/ocb/individual/VA/details/${vaId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.get<{ data: VirtualAccountDto }>(url, config)
      );
      return response.data.data;
    } catch (error) {
      this.logger.error(`Error getting virtual account details: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để lấy danh sách giao dịch
   * @param params Tham số tìm kiếm
   * @returns Danh sách giao dịch
   */
  async getTransactions(params?: {
    per_page?: string;
    page?: string;
    q?: string;
    company_id?: string;
    bank_id?: string;
    bank_account_id?: string;
    transaction_date?: string;
    start_transaction_date?: string;
    end_transaction_date?: string;
    transfer_type?: 'credit' | 'debit';
    va_id?: string;
  }): Promise<{ data: TransactionHistoryDto[]; meta: PaginationMetaDto }> {
    try {
      const url = `${this.baseUrl}/transaction`;
      const config = await this.createRequestConfig();

      if (params) {
        config.params = params;
      }

      const response = await lastValueFrom(
        this.httpService.get<{ data: TransactionHistoryDto[]; meta: PaginationMetaDto }>(url, config)
      );
      return {
        data: response.data.data,
        meta: response.data.meta,
      };
    } catch (error) {
      this.logger.error(`Error getting transactions: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để lấy thông tin chi tiết của một giao dịch
   * @param transactionId ID của giao dịch
   * @returns Thông tin chi tiết của giao dịch
   */
  async getTransactionDetails(transactionId: string): Promise<TransactionHistoryDto> {
    try {
      const url = `${this.baseUrl}/transaction/details/${transactionId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.get<{ data: TransactionHistoryDto }>(url, config)
      );
      return response.data.data;
    } catch (error) {
      this.logger.error(`Error getting transaction details: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để tra cứu tên chủ tài khoản OCB
   * @param request Thông tin tài khoản cần tra cứu
   * @returns Thông tin chủ tài khoản
   */
  async lookUpAccountHolderNameOCB(
    request: AccountLookupRequestDto
  ): Promise<{ account_holder_name: string }> {
    try {
      const url = `${this.baseUrl}/ocb/individual/bankAccount/lookUpAccountHolderName`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<{ data: { account_holder_name: string } }>(url, request, config)
      );
      return response.data.data;
    } catch (error) {
      this.logger.error(`Error looking up account holder name OCB: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để tạo tài khoản ngân hàng OCB
   * @param request Thông tin tài khoản cần tạo
   * @returns Thông tin tài khoản đã tạo
   */
  async createBankAccountOCB(
    request: BankAccountCreateRequestDto
  ): Promise<BankAccountCreateResponseDto> {
    try {
      const url = `${this.baseUrl}/ocb/individual/bankAccount/create`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<BankAccountCreateResponseDto>(url, request, config)
      );
      return response.data;
    } catch (error) {
      this.logger.error(`Error creating bank account OCB: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để chỉnh sửa tài khoản ngân hàng OCB
   * @param bankAccountId ID của tài khoản ngân hàng
   * @param request Thông tin cần chỉnh sửa
   * @returns Kết quả chỉnh sửa
   */
  async editBankAccountOCB(
    bankAccountId: string,
    request: EditBankAccountRequestDto
  ): Promise<{ success: boolean; message: string }> {
    try {
      const url = `${this.baseUrl}/ocb/individual/bankAccount/edit/${bankAccountId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<{ code: number; message: string }>(url, request, config)
      );
      return {
        success: response.data.code === 200,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error editing bank account OCB: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để tạo tài khoản ngân hàng ACB
   * @param request Thông tin tài khoản cần tạo
   * @returns Thông tin tài khoản đã tạo
   */
  async createBankAccountACB(
    request: BankAccountCreateRequestDto
  ): Promise<BankAccountCreateResponseDto> {
    try {
      const url = `${this.baseUrl}/acb/individual/bankAccount/create`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<BankAccountCreateResponseDto>(url, request, config)
      );
      return response.data;
    } catch (error) {
      this.logger.error(`Error creating bank account ACB: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để xác nhận kết nối API ACB
   * @param requestId ID của yêu cầu
   * @param request Thông tin OTP
   * @returns Kết quả xác nhận
   */
  async confirmApiConnectionACB(
    requestId: string,
    request: BankAccountConfirmRequestDto
  ): Promise<{ success: boolean; message: string }> {
    try {
      const url = `${this.baseUrl}/acb/individual/bankAccount/confirmApiConnection`;
      const config = await this.createRequestConfig();

      if (config.headers) {
        config.headers['Request-Id'] = requestId;
      }

      const response = await lastValueFrom(
        this.httpService.post<{ code: number; message: string }>(url, request, config)
      );
      return {
        success: response.data.code === 200,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error confirming API connection ACB: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để yêu cầu kết nối API ACB
   * @param bankAccountId ID của tài khoản ngân hàng
   * @returns Kết quả yêu cầu
   */
  async requestApiConnectionACB(
    bankAccountId: string
  ): Promise<{ request_id: string; message: string }> {
    try {
      const url = `${this.baseUrl}/acb/individual/bankAccount/requestApiConnection/${bankAccountId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<{ data: { request_id: string }; message: string }>(url, {}, config)
      );
      return {
        request_id: response.data.data.request_id,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error requesting API connection ACB: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để yêu cầu xóa kết nối API ACB
   * @param bankAccountId ID của tài khoản ngân hàng
   * @returns Kết quả yêu cầu
   */
  async requestDeleteApiConnectionACB(
    bankAccountId: string
  ): Promise<{ request_id: string; message: string }> {
    try {
      const url = `${this.baseUrl}/acb/individual/bankAccount/requestDelete/${bankAccountId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<{ data: { request_id: string }; message: string }>(url, {}, config)
      );
      return {
        request_id: response.data.data.request_id,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error requesting delete API connection ACB: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để xác nhận xóa kết nối API ACB
   * @param requestId ID của yêu cầu
   * @param request Thông tin OTP
   * @returns Kết quả xác nhận
   */
  async confirmDeleteApiConnectionACB(
    requestId: string,
    request: OtpRequestDto
  ): Promise<{ success: boolean; message: string }> {
    try {
      const url = `${this.baseUrl}/acb/individual/bankAccount/confirmDelete`;
      const config = await this.createRequestConfig();

      if (config.headers) {
        config.headers['Request-Id'] = requestId;
      }

      const response = await lastValueFrom(
        this.httpService.post<{ code: number; message: string }>(url, request, config)
      );
      return {
        success: response.data.code === 200,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error confirming delete API connection ACB: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để yêu cầu xóa tài khoản MB Bank
   * @param bankAccountId ID của tài khoản ngân hàng
   * @returns Kết quả yêu cầu
   */
  async requestDeleteMB(
    bankAccountId: string
  ): Promise<{ request_id: string; message: string }> {
    try {
      const url = `${this.baseUrl}/mb/individual/bankAccount/requestDelete/${bankAccountId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<{ data: { request_id: string }; message: string }>(url, {}, config)
      );
      return {
        request_id: response.data.data.request_id,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error requesting delete MB: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để xác nhận xóa tài khoản MB Bank
   * @param requestId ID của yêu cầu
   * @param request Thông tin OTP
   * @returns Kết quả xác nhận
   */
  async confirmDeleteMB(
    requestId: string,
    request: OtpRequestDto
  ): Promise<{ success: boolean; message: string }> {
    try {
      const url = `${this.baseUrl}/mb/individual/bankAccount/confirmDelete`;
      const config = await this.createRequestConfig();

      if (config.headers) {
        config.headers['Request-Id'] = requestId;
      }

      const response = await lastValueFrom(
        this.httpService.post<{ code: number; message: string }>(url, request, config)
      );
      return {
        success: response.data.code === 200,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error confirming delete MB: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để xóa tài khoản MB Bank bắt buộc
   * @param bankAccountId ID của tài khoản ngân hàng
   * @returns Kết quả xóa
   */
  async forceDeleteMB(
    bankAccountId: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const url = `${this.baseUrl}/mb/individual/bankAccount/forceDelete/${bankAccountId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<{ code: number; message: string }>(url, {}, config)
      );
      return {
        success: response.data.code === 200,
        message: response.data.message,
      };
    } catch (error) {
      this.logger.error(`Error force deleting MB: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gọi API để cập nhật thông tin công ty
   * @param companyId ID của công ty
   * @param request Thông tin cần cập nhật
   * @returns Kết quả cập nhật
   */
  async updateCompany(
    companyId: string,
    request: CompanyUpdateRequestDto
  ): Promise<CompanyDto> {
    try {
      const url = `${this.baseUrl}/company/edit/${companyId}`;
      const config = await this.createRequestConfig();
      const response = await lastValueFrom(
        this.httpService.post<{ data: CompanyDto }>(url, request, config)
      );
      return response.data.data;
    } catch (error) {
      this.logger.error(`Error updating company: ${error.message}`, error.stack);
      throw error;
    }
  }
}