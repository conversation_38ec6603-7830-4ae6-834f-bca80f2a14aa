import { ErrorCode } from '@common/exceptions/app.exception';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi liên quan đến module User (4000-4099)
 */
export const USER_ERROR_CODES = {
  // ===== USER BASIC ERRORS (4000-4019) =====
  /**
   * Lỗi khi không tìm thấy người dùng
   */
  USER_NOT_FOUND: new ErrorCode(
    4000,
    'Không tìm thấy người dùng',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi email đã tồn tại
   */
  EMAIL_ALREADY_EXISTS: new ErrorCode(
    4001,
    'Email đã được sử dụng',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi số điện thoại đã tồn tại
   */
  PHONE_NUMBER_ALREADY_EXISTS: new ErrorCode(
    4002,
    '<PERSON>ố điện thoại đã được sử dụng',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi tạo người dùng thất bại
   */
  USER_CREATION_FAILED: new ErrorCode(
    4003,
    'Tạo người dùng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật người dùng thất bại
   */
  USER_UPDATE_FAILED: new ErrorCode(
    4004,
    'Cập nhật người dùng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== USER STATUS ERRORS (4020-4039) =====
  /**
   * Lỗi khi người dùng đã bị khóa
   */
  USER_ALREADY_BLOCKED: new ErrorCode(
    4020,
    'Người dùng đã bị khóa trước đó',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi người dùng đã được kích hoạt
   */
  USER_ALREADY_ACTIVE: new ErrorCode(
    4021,
    'Người dùng đã được kích hoạt trước đó',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi khóa người dùng thất bại
   */
  USER_BLOCK_FAILED: new ErrorCode(
    4022,
    'Khóa người dùng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi mở khóa người dùng thất bại
   */
  USER_UNBLOCK_FAILED: new ErrorCode(
    4023,
    'Mở khóa người dùng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== USER AUTHENTICATION ERRORS (4040-4059) =====
  /**
   * Lỗi khi mật khẩu không đúng
   */
  INVALID_PASSWORD: new ErrorCode(
    4040,
    'Mật khẩu không chính xác',
    HttpStatus.UNAUTHORIZED,
  ),

  /**
   * Lỗi khi mật khẩu quá yếu
   */
  PASSWORD_TOO_WEAK: new ErrorCode(
    4041,
    'Mật khẩu không đủ mạnh',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi tài khoản chưa được xác thực
   */
  ACCOUNT_NOT_VERIFIED: new ErrorCode(
    4042,
    'Tài khoản chưa được xác thực',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi tài khoản đã bị khóa (cho authentication)
   */
  ACCOUNT_BLOCKED: new ErrorCode(
    4043,
    'Tài khoản đã bị khóa',
    HttpStatus.FORBIDDEN,
  ),

  // ===== USER PERMISSION ERRORS (4060-4079) =====
  /**
   * Lỗi khi không có quyền truy cập
   */
  ACCESS_DENIED: new ErrorCode(
    4060,
    'Bạn không có quyền thực hiện hành động này',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi vai trò không tồn tại
   */
  ROLE_NOT_FOUND: new ErrorCode(
    4061,
    'Vai trò không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  // ===== USER VALIDATION ERRORS (4080-4099) =====
  /**
   * Lỗi khi dữ liệu đầu vào không hợp lệ
   */
  INVALID_INPUT_DATA: new ErrorCode(
    4080,
    'Dữ liệu đầu vào không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi định dạng email không hợp lệ
   */
  INVALID_EMAIL_FORMAT: new ErrorCode(
    4081,
    'Định dạng email không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi định dạng số điện thoại không hợp lệ
   */
  INVALID_PHONE_FORMAT: new ErrorCode(
    4082,
    'Định dạng số điện thoại không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
};
