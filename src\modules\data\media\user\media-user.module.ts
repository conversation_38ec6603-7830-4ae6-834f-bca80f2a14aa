import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MediaUserController } from './controllers/media-user.controller';
import { MediaUserService } from './services/media-user.service';
import { MediaTrackingUserController } from './controllers/media-tracking-user.controller';
import { MediaTrackingUserService } from './services/media-tracking-user.service';
import { Media } from '../entities/media.entity';
import { MediaRepository } from '../repositories';
import { AuthModule } from '@modules/auth/auth.module';
import { AgentMedia } from '@modules/agent/entities';
import { DataSource } from 'typeorm';
import { AgentMediaRepository } from '@modules/agent/repositories/agent-media.repository';
import { MediaValidationHelper } from '../helpers/validation.helper';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';
import { HttpModule } from '@nestjs/axios';
import { UserRag<PERSON>piKey } from '@modules/user/entities/user-rag-api-key.entity';
import { UserRagApiKeyRepository } from '@modules/user/repositories/user-rag-api-key.repository';

@Module({
  imports: [TypeOrmModule.forFeature([Media, AgentMedia, UserRagApiKey]), AuthModule, HttpModule],
  controllers: [MediaUserController, MediaTrackingUserController],
  providers: [
    MediaUserService,
    {
      provide: MediaRepository,
      useFactory: (dataSource: DataSource) => {
        return new MediaRepository(dataSource);
      },
      inject: [DataSource],
    },
    {
      provide: AgentMediaRepository,
      useFactory: (dataSource: DataSource) => {
        return new AgentMediaRepository(dataSource);
      },
      inject: [DataSource],
    },
    {
      provide: UserRagApiKeyRepository,
      useFactory: (dataSource: DataSource) => {
        return new UserRagApiKeyRepository(dataSource);
      },
      inject: [DataSource],
    },
    MediaValidationHelper,
    RagFileProcessingService,
    MediaTrackingUserService,
  ],
  exports: [MediaUserService],
})
export class MediaUserModule {}
