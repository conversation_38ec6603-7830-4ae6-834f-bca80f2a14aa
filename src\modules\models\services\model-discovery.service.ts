import { AppException } from '@common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { AiProviderHelper } from '@shared/services/ai/helpers/ai-provider.helper';
import { ModelResponse } from '@shared/services/ai/interfaces/model-response.interface';
import { Transactional } from 'typeorm-transactional';
import { ProviderEnum } from '../constants/provider.enum';
import { ModelRegistry } from '../entities/model-registry.entity';
import { MODELS_ERROR_CODES } from '../exceptions/models.exception';
import { ModelRegistryRepository } from '../repositories/model-registry.repository';
import { SystemModelKeyLlmRepository } from '../repositories/system-model-key-llm.repository';
import { SystemModelsRepository } from '../repositories/system-models.repository';
import { UserModelKeyLlmRepository } from '../repositories/user-model-key-llm.repository';
import { UserModelsRepository } from '../repositories/user-models.repository';

/**
 * Interface cho kết quả discovery models
 */
export interface ModelDiscoveryResult {
  /** Tổng số models được tìm thấy từ provider */
  totalModelsFound: number;
  /** Số models match với patterns */
  modelsMatched: number;
  /** Số models mới được tạo */
  newModelsCreated: number;
  /** Số models đã tồn tại */
  existingModelsFound: number;
  /** Số mappings được tạo */
  mappingsCreated: number;
  /** Danh sách models đã được lưu */
  savedModels: Array<{
    modelId: string;
    modelRegistryId: string;
    isNew: boolean;
  }>;
  /** Danh sách lỗi nếu có */
  errors: string[];
}

/**
 * Service chính xử lý logic discovery models từ AI providers
 * Thực hiện pattern matching và lưu models vào database
 */
@Injectable()
export class ModelDiscoveryService {
  private readonly logger = new Logger(ModelDiscoveryService.name);

  constructor(
    private readonly aiProviderHelper: AiProviderHelper,
    private readonly modelRegistryRepository: ModelRegistryRepository,
    private readonly systemModelsRepository: SystemModelsRepository,
    private readonly userModelsRepository: UserModelsRepository,
    private readonly systemModelKeyLlmRepository: SystemModelKeyLlmRepository,
    private readonly userModelKeyLlmRepository: UserModelKeyLlmRepository,
  ) { }

  /**
   * Thực hiện discovery models cho system key
   * @param encryptedApiKey API key đã được mã hóa
   * @param provider Provider type
   * @param keyId ID của system key
   * @returns Kết quả discovery
   */
  @Transactional()
  async discoverSystemModels(
    encryptedApiKey: string,
    provider: ProviderEnum,
    keyId: string,
  ): Promise<ModelDiscoveryResult> {
    this.logger.log(`Starting system model discovery for provider: ${provider}, keyId: ${keyId}`);

    try {
      // 1. Lấy danh sách models từ provider
      const providerModels = await this.getModelsFromProvider(encryptedApiKey, provider, true);

      // 2. Lấy model patterns từ registry
      const modelPatterns = await this.getModelPatterns(provider);

      // 3. Thực hiện pattern matching
      const matchedModels = this.matchModelsWithPatterns(providerModels, modelPatterns);

      // 4. Lưu models vào system_models và tạo mappings
      const result = await this.saveSystemModels(matchedModels, keyId, provider);

      this.logger.log(`System model discovery completed. Created: ${result.newModelsCreated}, Existing: ${result.existingModelsFound}`);

      return result;
    } catch (error) {
      this.logger.error(`System model discovery failed: ${error.message}`, error.stack);
      throw new AppException(MODELS_ERROR_CODES.MODEL_DISCOVERY_FAILED, error.message);
    }
  }

  /**
   * Thực hiện discovery models cho user key
   * @param encryptedApiKey API key đã được mã hóa
   * @param provider Provider type
   * @param keyId ID của user key
   * @param userId ID của user
   * @returns Kết quả discovery
   */
  @Transactional()
  async discoverUserModels(
    encryptedApiKey: string,
    provider: ProviderEnum,
    keyId: string,
    userId: number,
  ): Promise<ModelDiscoveryResult> {
    this.logger.log(`Starting user model discovery for provider: ${provider}, keyId: ${keyId}, userId: ${userId}`);

    try {
      // 1. Lấy danh sách models từ provider
      const providerModels = await this.getModelsFromProvider(encryptedApiKey, provider, false, userId);

      // 2. Lấy model patterns từ registry
      const modelPatterns = await this.getModelPatterns(provider);

      // 3. Thực hiện pattern matching
      const matchedModels = this.matchModelsWithPatterns(providerModels, modelPatterns);

      // 4. Lưu models vào user_models và tạo mappings
      const result = await this.saveUserModels(matchedModels, keyId);

      this.logger.log(`User model discovery completed. Created: ${result.newModelsCreated}, Existing: ${result.existingModelsFound}`);

      return result;
    } catch (error) {
      this.logger.error(`User model discovery failed: ${error.message}`, error.stack);
      throw new AppException(MODELS_ERROR_CODES.MODEL_DISCOVERY_FAILED, error.message);
    }
  }

  /**
   * Lấy danh sách models từ AI provider
   * @param encryptedApiKey API key đã mã hóa
   * @param provider Provider type
   * @param isAdmin Có phải admin key không
   * @param userId User ID (cho user keys)
   * @returns Danh sách models
   */
  private async getModelsFromProvider(
    encryptedApiKey: string,
    provider: ProviderEnum,
    isAdmin: boolean,
    userId?: number,
  ): Promise<ModelResponse[]> {
    try {
      const modelsResponse = await this.aiProviderHelper.getModels(
        provider,
        encryptedApiKey,
        undefined, // limit
        isAdmin,
        userId,
      );

      this.logger.log(`Retrieved ${modelsResponse.length} models from provider: ${provider}`);
      return modelsResponse;
    } catch (error) {
      this.logger.error(`Failed to get models from provider ${provider}: ${error.message}`, error.stack);
      throw new AppException(MODELS_ERROR_CODES.PROVIDER_CONNECTION_FAILED, `Không thể lấy danh sách models từ ${provider}: ${error.message}`);
    }
  }

  /**
   * Lấy model patterns từ model registry theo provider
   * @param provider Provider type
   * @returns Danh sách model registry patterns
   */
  private async getModelPatterns(provider?: ProviderEnum): Promise<ModelRegistry[]> {
    try {
      // Nếu provider không được cung cấp, lấy tất cả patterns
      let patterns: ModelRegistry[];
      if (provider) {
        patterns = await this.modelRegistryRepository.findByProvider(provider);
      } else {
        // Lấy tất cả patterns từ tất cả providers
        patterns = await this.modelRegistryRepository
          .createQueryBuilder('modelRegistry')
          .where('modelRegistry.deletedAt IS NULL')
          .getMany();
      }

      this.logger.log(`Found ${patterns.length} model patterns for provider: ${provider || 'all'}`);
      return patterns;
    } catch (error) {
      this.logger.error(`Failed to get model patterns: ${error.message}`, error.stack);
      throw new AppException(MODELS_ERROR_CODES.MODEL_PATTERN_FETCH_FAILED, `Không thể lấy model patterns: ${error.message}`);
    }
  }

  /**
   * Lấy model ID từ ModelResponse (xử lý các loại model response khác nhau)
   * @param model Model response từ provider
   * @returns Model ID
   */
  private getModelId(model: ModelResponse): string {
    // Xử lý các loại model response khác nhau
    if ('id' in model && model.id) {
      return model.id;
    }

    // Xử lý GoogleAIModel - ưu tiên normalizedName nếu có
    if ('normalizedName' in model && model.normalizedName) {
      return model.normalizedName;
    }

    // Xử lý GoogleAIModel - fallback về name và chuẩn hóa
    if ('name' in model && model.name) {
      return this.normalizeModelId(model.name);
    }

    // Fallback cho các trường hợp khác
    if ('model' in model && typeof model.model === 'string') {
      return model.model;
    }

    // Nếu không tìm thấy ID, throw error
    throw new Error(`Cannot extract model ID from model response: ${JSON.stringify(model)}`);
  }

  /**
   * Thực hiện pattern matching giữa models và patterns
   * @param models Danh sách models từ provider
   * @param patterns Danh sách patterns từ registry
   * @returns Danh sách models đã match với registry ID
   */
  private matchModelsWithPatterns(
    models: ModelResponse[],
    patterns: ModelRegistry[],
  ): Array<{ model: ModelResponse; registryId: string }> {
    const matchedModels: Array<{ model: ModelResponse; registryId: string }> = [];

    for (const model of models) {
      const modelId = this.getModelId(model);

      // Tìm pattern phù hợp nhất (pattern ngắn nhất)
      const matchedPattern = patterns
        .filter(pattern => this.isModelMatchPattern(modelId, pattern.modelNamePattern))
        .sort((a, b) => a.modelNamePattern.length - b.modelNamePattern.length)[0];

      if (matchedPattern) {
        matchedModels.push({
          model,
          registryId: matchedPattern.id,
        });
        this.logger.debug(`Model ${modelId} matched with pattern: ${matchedPattern.modelNamePattern}`);
      } else {
        this.logger.debug(`Model ${modelId} did not match any pattern`);
      }
    }

    this.logger.log(`Matched ${matchedModels.length} models out of ${models.length} total models`);
    return matchedModels;
  }

  /**
   * Kiểm tra model có match với pattern không
   * @param modelId ID của model
   * @param pattern Pattern để match
   * @returns True nếu match
   */
  private isModelMatchPattern(modelId: string, pattern: string): boolean {
    // Chuẩn hóa modelId: loại bỏ prefix như "models/", "fineTuneModels/" để chỉ lấy tên model
    const normalizedModelId = this.normalizeModelId(modelId);

    // Chuyển pattern thành regex
    // Ví dụ: "gpt-*" -> "gpt-.*"
    const regexPattern = pattern
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.');

    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(normalizedModelId);
  }

  /**
   * Chuẩn hóa model ID bằng cách loại bỏ prefix
   * @param modelId Model ID gốc từ provider
   * @returns Model ID đã được chuẩn hóa
   */
  private normalizeModelId(modelId: string): string {
    // Xử lý Google AI models: loại bỏ "models/", "fineTuneModels/", etc.
    if (modelId.includes('/')) {
      return modelId.split('/').pop() || modelId;
    }

    // Trả về modelId gốc nếu không có prefix
    return modelId;
  }

  /**
   * Lưu system models và tạo mappings
   * @param matchedModels Danh sách models đã match
   * @param keyId ID của system key
   * @returns Kết quả lưu
   */
  private async saveSystemModels(
    matchedModels: Array<{ model: ModelResponse; registryId: string }>,
    keyId: string,
    provider: ProviderEnum
  ): Promise<ModelDiscoveryResult> {
    const result: ModelDiscoveryResult = {
      totalModelsFound: matchedModels.length,
      modelsMatched: matchedModels.length,
      newModelsCreated: 0,
      existingModelsFound: 0,
      mappingsCreated: 0,
      savedModels: [],
      errors: [],
    };

    for (const { model, registryId } of matchedModels) {
      try {
        const modelId = this.getModelId(model);

        // Kiểm tra model đã tồn tại chưa
        const existingModel = await this.systemModelsRepository.findByModelId(modelId);

        let systemModelId: string;
        let isNew = false;

        if (existingModel) {
          systemModelId = existingModel.id;
          result.existingModelsFound++;
          this.logger.debug(`System model ${modelId} already exists`);
        } else {
          // Tạo model mới
          const newModel = this.systemModelsRepository.create({
            modelId: modelId,
            modelRegistryId: registryId,
            provider: provider,
            active: true,
          });

          const savedModel = await this.systemModelsRepository.save(newModel);
          systemModelId = savedModel.id;
          result.newModelsCreated++;
          isNew = true;
          this.logger.debug(`Created new system model: ${modelId}`);
        }

        // Tạo mapping với key (luôn tạo mới hoặc update)
        await this.createOrUpdateSystemModelKeyMapping(systemModelId, keyId);
        result.mappingsCreated++;

        result.savedModels.push({
          modelId: modelId,
          modelRegistryId: registryId,
          isNew,
        });

      } catch (error) {
        const modelId = this.getModelId(model);
        const errorMsg = `Failed to save system model ${modelId}: ${error.message}`;
        this.logger.error(errorMsg, error.stack);
        result.errors.push(errorMsg);
      }
    }

    return result;
  }

  /**
   * Lưu user models và tạo mappings
   * @param matchedModels Danh sách models đã match
   * @param keyId ID của user key
   * @returns Kết quả lưu
   */
  private async saveUserModels(
    matchedModels: Array<{ model: ModelResponse; registryId: string }>,
    keyId: string,
  ): Promise<ModelDiscoveryResult> {
    const result: ModelDiscoveryResult = {
      totalModelsFound: matchedModels.length,
      modelsMatched: matchedModels.length,
      newModelsCreated: 0,
      existingModelsFound: 0,
      mappingsCreated: 0,
      savedModels: [],
      errors: [],
    };

    for (const { model, registryId } of matchedModels) {
      try {
        const modelId = this.getModelId(model);

        // Kiểm tra model đã tồn tại chưa
        const existingModel = await this.userModelsRepository.findByModelId(modelId);

        let userModelId: string;
        let isNew = false;

        if (existingModel) {
          userModelId = existingModel.id;
          result.existingModelsFound++;
          this.logger.debug(`User model ${modelId} already exists`);
        } else {
          // Tạo model mới
          const newModel = this.userModelsRepository.create({
            modelId: modelId,
            modelRegistryId: registryId,
          });

          const savedModel = await this.userModelsRepository.save(newModel);
          userModelId = savedModel.id;
          result.newModelsCreated++;
          isNew = true;
          this.logger.debug(`Created new user model: ${modelId}`);
        }

        // Tạo mapping với key (luôn tạo mới hoặc update)
        await this.createOrUpdateUserModelKeyMapping(userModelId, keyId);
        result.mappingsCreated++;

        result.savedModels.push({
          modelId: modelId,
          modelRegistryId: registryId,
          isNew,
        });

      } catch (error) {
        const modelId = this.getModelId(model);
        const errorMsg = `Failed to save user model ${modelId}: ${error.message}`;
        this.logger.error(errorMsg, error.stack);
        result.errors.push(errorMsg);
      }
    }

    return result;
  }

  /**
   * Tạo hoặc cập nhật mapping giữa system model và key
   * @param modelId ID của system model
   * @param keyId ID của system key
   */
  private async createOrUpdateSystemModelKeyMapping(modelId: string, keyId: string): Promise<void> {
    try {
      // Kiểm tra mapping đã tồn tại chưa
      const existingMapping = await this.systemModelKeyLlmRepository.findOne({
        where: { modelId, llmKeyId: keyId }
      });

      if (!existingMapping) {
        const mapping = this.systemModelKeyLlmRepository.create({
          modelId,
          llmKeyId: keyId,
        });

        await this.systemModelKeyLlmRepository.save(mapping);
        this.logger.debug(`Created system model key mapping: ${modelId} -> ${keyId}`);
      } else {
        this.logger.debug(`System model key mapping already exists: ${modelId} -> ${keyId}`);
      }
    } catch (error) {
      this.logger.error(`Failed to create system model key mapping: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo hoặc cập nhật mapping giữa user model và key
   * @param modelId ID của user model
   * @param keyId ID của user key
   */
  private async createOrUpdateUserModelKeyMapping(modelId: string, keyId: string): Promise<void> {
    try {
      // Kiểm tra mapping đã tồn tại chưa
      const existingMapping = await this.userModelKeyLlmRepository.findOne({
        where: { modelId, llmKeyId: keyId }
      });

      if (!existingMapping) {
        const mapping = this.userModelKeyLlmRepository.create({
          modelId,
          llmKeyId: keyId,
        });

        await this.userModelKeyLlmRepository.save(mapping);
        this.logger.debug(`Created user model key mapping: ${modelId} -> ${keyId}`);
      } else {
        this.logger.debug(`User model key mapping already exists: ${modelId} -> ${keyId}`);
      }
    } catch (error) {
      this.logger.error(`Failed to create user model key mapping: ${error.message}`, error.stack);
      throw error;
    }
  }
}
