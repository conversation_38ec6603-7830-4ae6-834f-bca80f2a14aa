# API Xóa Nhiều Affiliate Rank

## Tổng quan
API này cho phép xóa nhiều affiliate rank cùng lúc để tăng hiệu quả quản lý.

## Endpoint
```
DELETE /api/v1/admin/affiliate/ranks/bulk-delete
```

## Authentication
- <PERSON><PERSON><PERSON> cầu JWT token của admin
- Header: `Authorization: Bearer <admin_token>`

## Request Body
```typescript
{
  "ids": number[]  // Danh sách ID các rank cần xóa (1-50 items)
}
```

### Validation Rules
- `ids`: B<PERSON>t buộc, phải là mảng số
- Tối thiểu: 1 ID
- Tối đa: 50 ID mỗi lần
- Mỗi ID phải là số hợp lệ

## Response
```typescript
{
  "success": boolean,
  "message": string,
  "data": {
    "successCount": number,      // Số lượng xóa thành công
    "failureCount": number,      // <PERSON><PERSON> lượng xóa thất bại
    "totalCount": number,        // Tổng số yêu cầu xóa
    "successIds": number[],      // Danh sách ID xóa thành công
    "failures": Array<{         // Danh sách ID xóa thất bại
      "id": number,
      "reason": string
    }>,
    "message": string           // Thông báo tổng quan
  }
}
```

## Ví dụ

### Request thành công
```json
{
  "ids": [1, 2, 3]
}
```

### Response thành công hoàn toàn
```json
{
  "success": true,
  "message": "Xóa nhiều rank affiliate hoàn tất",
  "data": {
    "successCount": 3,
    "failureCount": 0,
    "totalCount": 3,
    "successIds": [1, 2, 3],
    "failures": [],
    "message": "Đã xóa thành công tất cả 3 rank affiliate"
  }
}
```

### Response thành công một phần
```json
{
  "success": true,
  "message": "Xóa nhiều rank affiliate hoàn tất",
  "data": {
    "successCount": 2,
    "failureCount": 1,
    "totalCount": 3,
    "successIds": [1, 2],
    "failures": [
      {
        "id": 3,
        "reason": "Không tìm thấy rank affiliate"
      }
    ],
    "message": "Đã xóa thành công 2/3 rank affiliate"
  }
}
```

## Error Codes
- `400`: Validation error (ID không hợp lệ, quá nhiều ID, etc.)
- `401`: Unauthorized (thiếu hoặc token không hợp lệ)
- `403`: Forbidden (không có quyền admin)
- `500`: Internal server error

## Lưu ý
- API sử dụng transaction để đảm bảo tính nhất quán
- Xử lý từng ID một cách tuần tự để tránh conflict
- Không throw error nếu một số ID xóa thất bại, mà trả về kết quả chi tiết
- Log chi tiết cho việc debug và audit
