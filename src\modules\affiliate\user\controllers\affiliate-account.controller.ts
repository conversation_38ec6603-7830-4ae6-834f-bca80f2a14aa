import { Controller, Get, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ApiResponseDto } from '@/common/response';
import { AffiliateAccountService } from '../services';
import { AffiliateAccountDto } from '../dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@Controller('user/affiliate/account')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_ACCOUNT)
@ApiExtraModels(ApiResponseDto, AffiliateAccountDto)
export class AffiliateAccountController {
  constructor(
    private readonly affiliateAccountService: AffiliateAccountService,
  ) {}

  /**
   * Lấy thông tin tài khoản affiliate
   * @param user Thông tin người dùng hiện tại
   * @returns Thông tin tài khoản affiliate bao gồm số dư khả dụng và số tiền đang xử lý
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy thông tin tài khoản affiliate',
    description: 'Lấy thông tin tài khoản affiliate bao gồm số dư khả dụng và số tiền đang xử lý (tổng amount trong affiliate_withdraw_history có status PENDING hoặc INVOICE_NOT_UPLOADED)'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin tài khoản affiliate thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(AffiliateAccountDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy tài khoản affiliate',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy thông tin tài khoản affiliate',
  })
  async getAccount(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<AffiliateAccountDto>> {
    const account = await this.affiliateAccountService.getAccount(user.id);
    return ApiResponseDto.success(
      account,
      'Lấy thông tin tài khoản affiliate thành công',
    );
  }
}
