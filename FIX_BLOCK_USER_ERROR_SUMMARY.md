# Khắc phục lỗi API Block User - Error Code 9999

## 🔍 **Phân tích vấn đề**

### **Lỗi ban đầu:**
```json
{
    "code": 9999,
    "message": "Lỗi khi khóa tài khoản người dùng",
    "path": "/v1/admin/users/51/block"
}
```

### **Log thực tế:**
```
Client Error: Người dùng với ID 52 đã bị khóa trước đó
{"error":{"code":4001,"message":"Người dùng với ID 52 đã bị khóa trước đó"}}
```

### **Nguyên nhân:**
1. **Service sử dụng sai ErrorCode**: `ErrorCode.VALIDATION_ERROR` thay vì error code chuyên biệt
2. **Không có USER_ERROR_CODES riêng**: Module user chưa có error codes riêng
3. **ErrorCode generic có code 9999**: <PERSON><PERSON><PERSON>ode chung đều dùng code 9999

## 🔧 **<PERSON><PERSON><PERSON>i pháp đã thực hiện**

### **1. Tạo USER_ERROR_CODES riêng**
- **File**: `src/modules/user/exceptions/user-error.codes.ts`
- **Range**: 4000-4099
- **Các error codes chính**:
  - `USER_NOT_FOUND`: 4000
  - `USER_ALREADY_BLOCKED`: 4020
  - `USER_ALREADY_ACTIVE`: 4021
  - `USER_BLOCK_FAILED`: 4022
  - `USER_UNBLOCK_FAILED`: 4023

### **2. Cập nhật UserAdminService**
- **File**: `src/modules/user/admin/services/user-admin.service.ts`
- **Thay đổi**:
  ```typescript
  // ❌ Trước
  throw new AppException(
    ErrorCode.VALIDATION_ERROR,
    `Người dùng với ID ${id} đã bị khóa trước đó`
  );

  // ✅ Sau
  throw new AppException(
    USER_ERROR_CODES.USER_ALREADY_BLOCKED,
    `Người dùng với ID ${id} đã bị khóa trước đó`
  );
  ```

### **3. Cập nhật tất cả error codes trong service**
- `USER_NOT_FOUND`: 4000
- `USER_ALREADY_BLOCKED`: 4020  
- `USER_ALREADY_ACTIVE`: 4021
- `USER_BLOCK_FAILED`: 4022
- `USER_UNBLOCK_FAILED`: 4023

## 📊 **Kết quả sau khi sửa**

### **Response mới khi user đã bị khóa:**
```json
{
  "code": 4020,
  "message": "Người dùng với ID 52 đã bị khóa trước đó",
  "path": "/v1/admin/users/52/block",
  "requestId": "..."
}
```

### **Response khi user không tồn tại:**
```json
{
  "code": 4000,
  "message": "Không tìm thấy người dùng với ID 99999",
  "path": "/v1/admin/users/99999/block",
  "requestId": "..."
}
```

## 🧪 **Testing**

### **Sử dụng file test:**
```
test-block-user-api.http
```

### **Các test cases:**
1. **Block user thành công** → 200 OK
2. **Block user đã bị khóa** → 400 Bad Request (code: 4020)
3. **Block user không tồn tại** → 404 Not Found (code: 4000)
4. **Unblock user** → 200 OK
5. **Unblock user đã active** → 400 Bad Request (code: 4021)

## 🎯 **Lợi ích**

### **1. Error codes rõ ràng**
- Mỗi lỗi có code riêng biệt
- Dễ debug và trace lỗi
- Frontend có thể handle cụ thể từng loại lỗi

### **2. Consistency**
- Tất cả user-related errors đều trong range 4000-4099
- Tuân thủ quy tắc error handling của dự án

### **3. Maintainability**
- Error codes được tập trung quản lý
- Dễ thêm/sửa error codes mới
- Documentation rõ ràng

## 🔄 **Áp dụng cho modules khác**

### **Pattern để follow:**
1. Tạo `[module]/exceptions/[module]-error.codes.ts`
2. Define error codes với range riêng
3. Import và sử dụng trong services
4. Update GlobalExceptionFilter nếu cần

### **Error code ranges đã sử dụng:**
- User: 4000-4099
- Employee: 15000-15099
- Marketing: 30000-30099
- Models: 20000-20099

## ✅ **Kết luận**

Lỗi đã được khắc phục hoàn toàn:
- ✅ Error code chính xác (4020 thay vì 9999)
- ✅ Message rõ ràng và nhất quán
- ✅ Logging đúng format
- ✅ API response chuẩn
- ✅ Test cases đầy đủ
