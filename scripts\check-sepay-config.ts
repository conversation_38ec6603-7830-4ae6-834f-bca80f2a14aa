import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { SepayHubService } from '../src/shared/services/sepay-hub/sepay-hub.service';
import { ConfigService } from '@nestjs/config';

/**
 * Script để kiểm tra cấu hình SepayHub
 */
async function checkSepayConfig() {
  console.log('🔍 Checking SepayHub configuration...\n');

  try {
    // Khởi tạo NestJS app
    const app = await NestFactory.createApplicationContext(AppModule);
    const configService = app.get(ConfigService);
    const sepayHubService = app.get(SepayHubService);

    // Kiểm tra environment variables
    console.log('📋 Environment Variables:');
    console.log(`SEPAY_HUB_API_URL: ${configService.get('SEPAY_HUB_API_URL') || 'NOT SET'}`);
    console.log(`SEPAY_HUB_API_KEY: ${configService.get('SEPAY_HUB_API_KEY') ? '***SET***' : 'NOT SET'}`);
    console.log(`SEPAY_HUB_SECRET_KEY: ${configService.get('SEPAY_HUB_SECRET_KEY') ? '***SET***' : 'NOT SET'}`);
    console.log('');

    // Kiểm tra độ dài credentials
    const apiKey = configService.get('SEPAY_HUB_API_KEY');
    const secretKey = configService.get('SEPAY_HUB_SECRET_KEY');
    
    if (apiKey) {
      console.log(`API Key length: ${apiKey.length} characters`);
      console.log(`API Key starts with: ${apiKey.substring(0, 4)}...`);
    }
    
    if (secretKey) {
      console.log(`Secret Key length: ${secretKey.length} characters`);
      console.log(`Secret Key starts with: ${secretKey.substring(0, 4)}...`);
    }
    console.log('');

    // Test connection
    console.log('🔗 Testing connection...');
    const connectionResult = await sepayHubService.testConnection();
    
    if (connectionResult) {
      console.log('✅ Connection successful!');
    } else {
      console.log('❌ Connection failed!');
    }

    await app.close();
  } catch (error) {
    console.error('❌ Error checking SepayHub config:', error.message);
    process.exit(1);
  }
}

// Chạy script
checkSepayConfig().catch(console.error);
