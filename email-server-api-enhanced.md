# Email Server Configuration API - Enhanced

## Tổng quan thay đổi

### 🔧 **<PERSON><PERSON><PERSON> tính năng đã thêm:**

1. **Validation trùng lặp**: Không cho phép tạo 2 máy chủ email giống nhau cho cùng 1 user
2. **Tích hợp với Integration**: Tự động tạo bản ghi Integration khi tạo EmailServerConfiguration
3. **Cascade delete**: <PERSON><PERSON><PERSON> cả Integration khi xóa EmailServerConfiguration
4. **Response mở rộng**: Thêm trường `integrationId` vào response

---

## API Endpoints

### 1. Tạo mới cấu hình máy chủ email

**Endpoint:** `POST /user/integration/email-server`

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Request Body:**
```json
{
  "serverName": "Gmail SMTP Server",
  "host": "smtp.gmail.com",
  "port": 587,
  "username": "<EMAIL>",
  "password": "app_password_here",
  "useSsl": false,
  "useStartTls": true,
  "isActive": true,
  "additionalSettings": {
    "timeout": 5000
  }
}
```

**Response thành công (201):**
```json
{
  "success": true,
  "message": "Tạo mới cấu hình máy chủ email thành công",
  "result": {
    "id": 123,
    "userId": 456,
    "integrationId": 789,
    "serverName": "Gmail SMTP Server",
    "host": "smtp.gmail.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "********",
    "useSsl": false,
    "useStartTls": true,
    "isActive": true,
    "additionalSettings": {
      "timeout": 5000
    },
    "createdAt": 1672531200,
    "updatedAt": 1672531200
  }
}
```

**Response lỗi trùng lặp (409):**
```json
{
  "success": false,
  "message": "Máy chủ email với host \"smtp.gmail.com\", port \"587\" và username \"<EMAIL>\" đã tồn tại cho người dùng này",
  "error": "Conflict"
}
```

---

## Validation Rules

### Kiểm tra trùng lặp
Hệ thống kiểm tra trùng lặp dựa trên **3 trường**:
- `host` (ví dụ: smtp.gmail.com)
- `port` (ví dụ: 587)
- `username` (ví dụ: <EMAIL>)

**Cho cùng 1 user**, không được phép tạo 2 cấu hình có cùng 3 trường này.

### Ví dụ validation:

✅ **Được phép:**
```json
// Cấu hình 1
{"host": "smtp.gmail.com", "port": 587, "username": "<EMAIL>"}

// Cấu hình 2 (khác username)
{"host": "smtp.gmail.com", "port": 587, "username": "<EMAIL>"}
```

❌ **Không được phép:**
```json
// Cấu hình 1
{"host": "smtp.gmail.com", "port": 587, "username": "<EMAIL>"}

// Cấu hình 2 (trùng lặp hoàn toàn)
{"host": "smtp.gmail.com", "port": 587, "username": "<EMAIL>"}
```

---

## Integration Record

### Tự động tạo Integration
Khi tạo EmailServerConfiguration, hệ thống sẽ:

1. **Tạo bản ghi Integration** với thông tin:
   ```json
   {
     "integrationName": "Gmail SMTP Server",
     "type": "EMAIL_SMTP",
     "userId": 456,
     "ownedType": "USER",
     "info": {
       "host": "smtp.gmail.com",
       "port": 587,
       "username": "<EMAIL>",
       "useSsl": false,
       "useStartTls": true
     }
   }
   ```

2. **Gắn integrationId** vào EmailServerConfiguration

### Cascade Delete
Khi xóa EmailServerConfiguration:
- Tự động xóa bản ghi Integration liên kết
- Nếu xóa Integration thất bại, vẫn tiếp tục xóa EmailServerConfiguration

---

## Test Cases

### Test 1: Tạo thành công
```bash
curl -X POST "http://localhost:3000/user/integration/email-server" \
  -H "Authorization: Bearer {JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "Gmail SMTP",
    "host": "smtp.gmail.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "app_password",
    "useSsl": false,
    "useStartTls": true
  }'

# Expected: 201 Created với integrationId
```

### Test 2: Validation trùng lặp
```bash
# Tạo lần 1 (thành công)
curl -X POST "http://localhost:3000/user/integration/email-server" \
  -H "Authorization: Bearer {JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "Gmail SMTP 1",
    "host": "smtp.gmail.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "password1"
  }'

# Tạo lần 2 (thất bại - trùng lặp)
curl -X POST "http://localhost:3000/user/integration/email-server" \
  -H "Authorization: Bearer {JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "serverName": "Gmail SMTP 2",
    "host": "smtp.gmail.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "password2"
  }'

# Expected: 409 Conflict
```

### Test 3: Xóa cascade
```bash
# Tạo email server
POST_RESPONSE=$(curl -X POST "http://localhost:3000/user/integration/email-server" \
  -H "Authorization: Bearer {JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{"serverName": "Test Server", "host": "smtp.test.com", "port": 587, "username": "<EMAIL>", "password": "password"}')

EMAIL_SERVER_ID=$(echo $POST_RESPONSE | jq -r '.result.id')
INTEGRATION_ID=$(echo $POST_RESPONSE | jq -r '.result.integrationId')

# Kiểm tra Integration tồn tại
curl -X GET "http://localhost:3000/admin/integration/$INTEGRATION_ID" \
  -H "Authorization: Bearer {ADMIN_JWT_TOKEN}"

# Xóa email server
curl -X DELETE "http://localhost:3000/user/integration/email-server/$EMAIL_SERVER_ID" \
  -H "Authorization: Bearer {JWT_TOKEN}"

# Kiểm tra Integration đã bị xóa
curl -X GET "http://localhost:3000/admin/integration/$INTEGRATION_ID" \
  -H "Authorization: Bearer {ADMIN_JWT_TOKEN}"

# Expected: 404 Not Found
```

---

## Database Schema

### EmailServerConfiguration
```sql
ALTER TABLE email_server_configurations 
ADD COLUMN integration_id INTEGER NULL;
```

### Integration
```sql
-- Bản ghi Integration sẽ có:
-- type = 'EMAIL_SMTP'
-- owned_type = 'USER'
-- info = JSON với thông tin SMTP
```

---

## Error Codes

| HTTP Code | Error | Description |
|-----------|-------|-------------|
| 201 | - | Tạo thành công |
| 400 | Bad Request | Dữ liệu đầu vào không hợp lệ |
| 409 | Conflict | Máy chủ email đã tồn tại |
| 404 | Not Found | Không tìm thấy cấu hình |
| 500 | Internal Server Error | Lỗi hệ thống |
