import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AffiliateRankService } from '@modules/affiliate/admin/services';
import { AffiliateRankDto, AffiliateRankQueryDto, AffiliateRankStatisticsDto, CreateAffiliateRankDto, CreateAffiliateRankResponseDto, BulkDeleteAffiliateRankDto, BulkDeleteAffiliateRankResponseDto } from '../dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { AppException } from '@common/exceptions';
import { Logger } from '@nestjs/common';

/**
 * Controller xử lý các API liên quan đến quản lý rank affiliate cho admin
 */
@Controller('admin/affiliate/ranks')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.ADMIN_AFFILIATE_RANK)
@ApiExtraModels(
  ApiResponseDto,
  AffiliateRankDto,
  PaginatedResult,
  ApiErrorResponseDto,
  AffiliateRankStatisticsDto,
  CreateAffiliateRankDto,
  CreateAffiliateRankResponseDto,
  BulkDeleteAffiliateRankDto,
  BulkDeleteAffiliateRankResponseDto
)
export class AffiliateRankController {
  private readonly logger = new Logger(AffiliateRankController.name);

  constructor(private readonly affiliateRankService: AffiliateRankService) {}

  /**
   * Lấy danh sách rank affiliate với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách rank affiliate với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách rank affiliate',
    description: 'Lấy danh sách rank affiliate với phân trang, hỗ trợ tìm kiếm và lọc theo trạng thái kích hoạt'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách rank affiliate thành công',
    schema: ApiResponseDto.getPaginatedSchema(AffiliateRankDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED
  )
  async getRanks(
    @Query() queryDto: AffiliateRankQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AffiliateRankDto>>> {
    const ranks = await this.affiliateRankService.getRanks(queryDto);
    return ApiResponseDto.success(
      ranks,
      'Lấy danh sách rank affiliate thành công',
    );
  }

  /**
   * Lấy thông tin chi tiết rank affiliate theo ID
   * @param id ID của rank affiliate
   * @returns Thông tin chi tiết rank affiliate
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết rank affiliate',
    description: 'Lấy thông tin chi tiết của một rank affiliate dựa trên ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của rank affiliate',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết rank affiliate thành công',
    schema: ApiResponseDto.getSchema(AffiliateRankDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.RANK_NOT_FOUND
  )
  async getRankById(
    @Param('id') id: number,
  ): Promise<ApiResponseDto<AffiliateRankDto>> {
    try {
      // Kiểm tra id có hợp lệ không
      if (id === null || id === undefined || isNaN(Number(id))) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.RANK_NOT_FOUND,
          'ID rank affiliate không hợp lệ',
        );
      }

      const rank = await this.affiliateRankService.getRankById(Number(id));
      return ApiResponseDto.success(
        rank,
        'Lấy thông tin chi tiết rank affiliate thành công',
      );
    } catch (error) {
      this.logger.error(`Error in getRankById: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AFFILIATE_ERROR_CODES.RANK_NOT_FOUND,
        'Lỗi khi lấy thông tin rank affiliate',
      );
    }
  }

  /**
   * Tạo rank affiliate mới
   * @param createRankDto Thông tin rank cần tạo
   * @returns Thông tin rank đã tạo và URL tạm thời để upload ảnh (nếu có)
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo rank affiliate mới',
    description: 'Tạo một rank affiliate mới với các thông tin như tên, hoa hồng, điều kiện min/max, v.v.'
  })
  @ApiBody({
    type: CreateAffiliateRankDto,
    description: 'Thông tin rank affiliate cần tạo'
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo rank affiliate mới thành công',
    schema: ApiResponseDto.getSchema(CreateAffiliateRankResponseDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.RANK_CREATION_FAILED,
    AFFILIATE_ERROR_CODES.RANK_CONDITION_OVERLAP,
    AFFILIATE_ERROR_CODES.INVALID_RANK_CONDITION
  )
  async createRank(
    @Body()
    createRankDto: CreateAffiliateRankDto,
  ): Promise<ApiResponseDto<CreateAffiliateRankResponseDto>> {
    const rank = await this.affiliateRankService.createRank(createRankDto);
    return ApiResponseDto.created(rank, 'Tạo rank affiliate mới thành công');
  }

  /**
   * Cập nhật thông tin rank affiliate
   * @param id ID của rank affiliate
   * @param updateRankDto Thông tin cần cập nhật
   * @returns Thông tin rank đã cập nhật
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Cập nhật thông tin rank affiliate',
    description: 'Cập nhật thông tin của một rank affiliate dựa trên ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của rank affiliate',
    type: 'number',
    example: 1
  })
  @ApiBody({
    type: AffiliateRankDto,
    description: 'Thông tin rank affiliate cần cập nhật'
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thông tin rank affiliate thành công',
    schema: ApiResponseDto.getSchema(AffiliateRankDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.RANK_NOT_FOUND,
    AFFILIATE_ERROR_CODES.RANK_UPDATE_FAILED,
    AFFILIATE_ERROR_CODES.RANK_CONDITION_OVERLAP,
    AFFILIATE_ERROR_CODES.INVALID_RANK_CONDITION
  )
  async updateRank(
    @Param('id') id: number,
    @Body()
    updateRankDto: Partial<
      Omit<AffiliateRankDto, 'id' | 'createdAt' | 'updatedAt'>
    >,
  ): Promise<ApiResponseDto<AffiliateRankDto>> {
    const rank = await this.affiliateRankService.updateRank(id, updateRankDto);
    return ApiResponseDto.updated(
      rank,
      'Cập nhật thông tin rank affiliate thành công',
    );
  }

  /**
   * Cập nhật trạng thái kích hoạt của rank affiliate
   * @param id ID của rank affiliate
   * @param body Thông tin cập nhật
   * @returns Thông tin rank đã cập nhật
   */
  @Patch(':id/status')
  @ApiOperation({
    summary: 'Cập nhật trạng thái kích hoạt của rank affiliate',
    description: 'Kích hoạt hoặc vô hiệu hóa một rank affiliate dựa trên ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của rank affiliate',
    type: 'number',
    example: 1
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        isActive: {
          type: 'boolean',
          description: 'Trạng thái kích hoạt mới',
          example: true
        }
      },
      required: ['isActive']
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái kích hoạt của rank affiliate thành công',
    schema: ApiResponseDto.getSchema(AffiliateRankDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.RANK_NOT_FOUND,
    AFFILIATE_ERROR_CODES.RANK_UPDATE_FAILED
  )
  async updateRankStatus(
    @Param('id') id: number,
    @Body() body: { isActive: boolean },
  ): Promise<ApiResponseDto<AffiliateRankDto>> {
    const rank = await this.affiliateRankService.updateRankStatus(
      id,
      body.isActive,
    );
    return ApiResponseDto.updated(
      rank,
      'Cập nhật trạng thái kích hoạt của rank affiliate thành công',
    );
  }

  /**
   * Lấy khoảng minCondition và maxCondition đã sử dụng
   * @returns Danh sách khoảng minCondition và maxCondition đã sử dụng
   */
  @Get('info/condition-ranges')
  @ApiOperation({
    summary: 'Lấy khoảng minCondition và maxCondition đã sử dụng',
    description: 'Lấy danh sách các khoảng điều kiện (minCondition và maxCondition) đã được sử dụng trong các rank hiện có'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy khoảng điều kiện đã sử dụng thành công',
    schema: ApiResponseDto.getArraySchema({
      type: 'object',
      properties: {
        minCondition: {
          type: 'number',
          example: 0,
          description: 'Giá trị điều kiện tối thiểu'
        },
        maxCondition: {
          type: 'number',
          example: 10,
          description: 'Giá trị điều kiện tối đa'
        }
      }
    })
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED
  )
  async getUsedConditionRanges(): Promise<ApiResponseDto<{ minCondition: number; maxCondition: number }[]>> {
    // Gọi service để lấy khoảng điều kiện đã sử dụng
    const conditionRanges = await this.affiliateRankService.getUsedConditionRanges();

    // Trả về kết quả thành công
    return ApiResponseDto.success(conditionRanges, 'Lấy khoảng điều kiện đã sử dụng thành công');
  }

  /**
   * Lấy thống kê về rank affiliate
   * @returns Thống kê về rank affiliate
   */
  @Get('info/statistics')
  @ApiOperation({
    summary: 'Lấy thống kê về rank affiliate',
    description: 'Lấy thông tin thống kê tổng quan về rank affiliate, bao gồm phân bố tài khoản và doanh thu theo rank'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê về rank affiliate thành công',
    schema: ApiResponseDto.getSchema(AffiliateRankStatisticsDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED
  )
  async getRankStatistics(): Promise<ApiResponseDto<AffiliateRankStatisticsDto>> {
    const statistics = await this.affiliateRankService.getStatistics();
    return ApiResponseDto.success(statistics, 'Lấy thống kê về rank affiliate thành công');
  }

  /**
   * Xóa rank affiliate
   * @param id ID của rank affiliate cần xóa
   * @returns Thông báo xóa thành công
   */
  @Delete('delete/:id')
  @ApiOperation({
    summary: 'Xóa rank affiliate',
    description: 'Xóa một rank affiliate dựa trên ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của rank affiliate cần xóa',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa rank affiliate thành công',
    schema: ApiResponseDto.getSchema({
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true,
          description: 'Trạng thái xóa thành công'
        }
      }
    })
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.RANK_NOT_FOUND,
    AFFILIATE_ERROR_CODES.RANK_DELETION_FAILED
  )
  async deleteRank(
    @Param('id') id: number,
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    try {
      // Kiểm tra id có hợp lệ không
      if (id === null || id === undefined || isNaN(Number(id))) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.RANK_NOT_FOUND,
          'ID rank affiliate không hợp lệ',
        );
      }

      await this.affiliateRankService.deleteRank(Number(id));
      return ApiResponseDto.success(
        { success: true },
        'Xóa rank affiliate thành công',
      );
    } catch (error) {
      this.logger.error(`Error in deleteRank: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AFFILIATE_ERROR_CODES.RANK_DELETION_FAILED,
        'Lỗi khi xóa rank affiliate',
      );
    }
  }

  /**
   * Xóa nhiều rank affiliate
   * @param bulkDeleteDto Danh sách ID các rank cần xóa
   * @returns Kết quả xóa nhiều rank
   */
  @Delete('bulk-delete')
  @ApiOperation({
    summary: 'Xóa nhiều rank affiliate',
    description: 'Xóa nhiều rank affiliate cùng lúc dựa trên danh sách ID. Tối đa 50 rank mỗi lần.'
  })
  @ApiBody({
    type: BulkDeleteAffiliateRankDto,
    description: 'Danh sách ID các rank affiliate cần xóa'
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa nhiều rank affiliate hoàn tất (có thể một phần)',
    schema: ApiResponseDto.getSchema(BulkDeleteAffiliateRankResponseDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.RANK_DELETION_FAILED
  )
  async bulkDeleteRanks(
    @Body() bulkDeleteDto: BulkDeleteAffiliateRankDto,
  ): Promise<ApiResponseDto<BulkDeleteAffiliateRankResponseDto>> {
    try {
      this.logger.log(`Bắt đầu xóa bulk ${bulkDeleteDto.ids.length} rank affiliate`);

      const result = await this.affiliateRankService.bulkDeleteRanks(bulkDeleteDto);

      // Trả về kết quả với thông báo phù hợp
      return ApiResponseDto.success(result, result.message);
    } catch (error) {
      this.logger.error(`Error in bulkDeleteRanks: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AFFILIATE_ERROR_CODES.RANK_DELETION_FAILED,
        'Lỗi khi xóa nhiều rank affiliate',
      );
    }
  }
}
