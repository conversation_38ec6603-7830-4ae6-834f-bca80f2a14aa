import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { AffiliateWithdrawHistory } from '../entities/affiliate-withdraw-history.entity';
import { AffiliateWithdrawalQueryDto as UserWithdrawalQueryDto } from '../user/dto';
import { AffiliateWithdrawalQueryDto, WithdrawalStatus } from '../admin/dto';
import { PaginatedResult } from '@/common/response';

/**
 * Repository cho AffiliateWithdrawHistory
 * Extends Repository<AffiliateWithdrawHistory> theo Repository Standard #2
 */
@Injectable()
export class AffiliateWithdrawHistoryRepository extends Repository<AffiliateWithdrawHistory> {
  constructor(dataSource: DataSource) {
    super(AffiliateWithdrawHistory, dataSource.createEntityManager());
  }

  /**
   * Tìm danh sách yêu cầu rút tiền với phân trang
   * @param affiliateAccountId ID tài khoản affiliate
   * @param queryDto Tham số truy vấn
   * @returns Danh sách yêu cầu rút tiền với phân trang
   */
  async findWithPagination(
    affiliateAccountId: number,
    queryDto: UserWithdrawalQueryDto,
  ): Promise<PaginatedResult<AffiliateWithdrawHistory>> {
    const {
      page = 1,
      limit = 10,
      begin,
      end,
      status,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng query
    const queryBuilder = this.createQueryBuilder('withdrawal').where(
      'withdrawal.affiliateAccountId = :affiliateAccountId',
      {
        affiliateAccountId,
      },
    );

    // Thêm điều kiện thời gian nếu có
    if (begin) {
      queryBuilder.andWhere('withdrawal.createdAt >= :begin', { begin });
    }

    if (end) {
      queryBuilder.andWhere('withdrawal.createdAt <= :end', { end });
    }

    // Thêm điều kiện trạng thái nếu có
    if (status) {
      queryBuilder.andWhere('withdrawal.status = :status', { status });
    }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Thêm sắp xếp và phân trang
    queryBuilder
      .orderBy(`withdrawal.${sortBy}`, sortDirection)
      .skip(skip)
      .take(limit);

    // Lấy dữ liệu
    const items = await queryBuilder.getMany();

    // Tính toán metadata
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Tìm danh sách yêu cầu rút tiền với phân trang cho admin
   * @param queryDto Tham số truy vấn
   * @returns Danh sách yêu cầu rút tiền với phân trang
   */
  async findWithPaginationForAdmin(
    queryDto: AffiliateWithdrawalQueryDto,
  ): Promise<PaginatedResult<AffiliateWithdrawHistory>> {
    const {
      page = 1,
      limit = 10,
      begin,
      end,
      status,
      affiliateAccountId,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng query
    const queryBuilder = this.createQueryBuilder('withdrawal');

    // Thêm điều kiện tài khoản affiliate nếu có
    if (affiliateAccountId) {
      queryBuilder.andWhere(
        'withdrawal.affiliateAccountId = :affiliateAccountId',
        { affiliateAccountId },
      );
    }

    // Thêm điều kiện thời gian nếu có
    if (begin) {
      queryBuilder.andWhere('withdrawal.createdAt >= :begin', { begin });
    }

    if (end) {
      queryBuilder.andWhere('withdrawal.createdAt <= :end', { end });
    }

    // Thêm điều kiện trạng thái nếu có
    if (status) {
      queryBuilder.andWhere('withdrawal.status = :status', { status });
    }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Thêm sắp xếp và phân trang
    queryBuilder
      .orderBy(`withdrawal.${sortBy}`, sortDirection)
      .skip(skip)
      .take(limit);

    // Lấy dữ liệu
    const items = await queryBuilder.getMany();

    // Tính toán metadata
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Tìm yêu cầu rút tiền theo ID
   * @param id ID của yêu cầu rút tiền
   * @returns Thông tin yêu cầu rút tiền hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<AffiliateWithdrawHistory | null> {
    return this.findOne({ where: { id } });
  }

  /**
   * Cập nhật trạng thái yêu cầu rút tiền
   * @param id ID của yêu cầu rút tiền
   * @param status Trạng thái mới
   * @param employeeId ID của nhân viên xử lý
   * @param employeeName Tên của nhân viên xử lý
   * @param finishAt Thời gian hoàn thành
   * @param rejectReason Lý do từ chối (nếu từ chối)
   * @returns Kết quả cập nhật
   */
  async updateStatus(
    id: number,
    status: WithdrawalStatus,
    employeeId: number,
    finishAt: number,
    rejectReason?: string,
  ): Promise<void> {
    const updateData: Partial<AffiliateWithdrawHistory> = {
      status,
      processedByEmployeeId: employeeId,
      finishAt,
    };

    if (rejectReason) {
      updateData.rejectReason = rejectReason;
    }

    await this.update(id, updateData);
  }

  /**
   * Lấy thống kê tổng quan về yêu cầu rút tiền
   * @returns Thống kê tổng quan
   */
  async getWithdrawalOverview(): Promise<{
    totalWithdrawals: number;
    totalAmount: number;
    totalApprovedAmount: number;
    totalPendingAmount: number;
    totalRejectedAmount: number;
    pendingCount: number;
    approvedCount: number;
    rejectedCount: number;
    averageWithdrawalAmount: number;
  }> {
    // Đếm tổng số yêu cầu rút tiền
    const totalWithdrawals = await this.count();

    // Tính tổng số tiền đã yêu cầu rút
    const totalAmountResult = await this
      .createQueryBuilder('withdrawal')
      .select('SUM(withdrawal.amount)', 'total')
      .getRawOne();
    const totalAmount = parseFloat(totalAmountResult?.total) || 0;

    // Tính tổng số tiền đã phê duyệt
    const totalApprovedAmountResult = await this
      .createQueryBuilder('withdrawal')
      .select('SUM(withdrawal.amount)', 'total')
      .where('withdrawal.status = :status', { status: WithdrawalStatus.APPROVED })
      .getRawOne();
    const totalApprovedAmount = parseFloat(totalApprovedAmountResult?.total) || 0;

    // Tính tổng số tiền đang chờ duyệt
    const totalPendingAmountResult = await this
      .createQueryBuilder('withdrawal')
      .select('SUM(withdrawal.amount)', 'total')
      .where('withdrawal.status = :status', { status: WithdrawalStatus.PENDING })
      .getRawOne();
    const totalPendingAmount = parseFloat(totalPendingAmountResult?.total) || 0;

    // Tính tổng số tiền đã từ chối
    const totalRejectedAmountResult = await this
      .createQueryBuilder('withdrawal')
      .select('SUM(withdrawal.amount)', 'total')
      .where('withdrawal.status = :status', { status: WithdrawalStatus.REJECTED })
      .getRawOne();
    const totalRejectedAmount = parseFloat(totalRejectedAmountResult?.total) || 0;

    // Đếm số lượng yêu cầu theo trạng thái
    const pendingCount = await this.count({
      where: { status: WithdrawalStatus.PENDING }
    });
    const approvedCount = await this.count({
      where: { status: WithdrawalStatus.APPROVED }
    });
    const rejectedCount = await this.count({
      where: { status: WithdrawalStatus.REJECTED }
    });

    // Tính số tiền rút trung bình
    const averageWithdrawalAmount = totalWithdrawals > 0 ? totalAmount / totalWithdrawals : 0;

    return {
      totalWithdrawals,
      totalAmount,
      totalApprovedAmount,
      totalPendingAmount,
      totalRejectedAmount,
      pendingCount,
      approvedCount,
      rejectedCount,
      averageWithdrawalAmount
    };
  }

  /**
   * Tính tổng số tiền đang xử lý cho một tài khoản affiliate
   * @param affiliateAccountId ID tài khoản affiliate
   * @returns Tổng số tiền đang xử lý
   */
  async getProcessingAmountByAffiliateAccountId(affiliateAccountId: number): Promise<number> {
    const result = await this
      .createQueryBuilder('withdrawal')
      .select('SUM(withdrawal.amount)', 'total')
      .where('withdrawal.affiliateAccountId = :affiliateAccountId', { affiliateAccountId })
      .andWhere('withdrawal.status IN (:...statuses)', {
        statuses: ['PENDING', 'INVOICE_NOT_UPLOADED']
      })
      .getRawOne();

    return parseFloat(result?.total) || 0;
  }

  /**
   * Lấy thống kê phân bố theo trạng thái
   * @returns Thống kê phân bố theo trạng thái
   */
  async getStatusDistribution(): Promise<{
    status: WithdrawalStatus;
    count: number;
    percentage: number;
    totalAmount: number;
  }[]> {
    // Đếm tổng số yêu cầu rút tiền
    const totalWithdrawals = await this.count();

    // Lấy thống kê theo trạng thái
    const statusStats = await this
      .createQueryBuilder('withdrawal')
      .select('withdrawal.status', 'status')
      .addSelect('COUNT(withdrawal.id)', 'count')
      .addSelect('SUM(withdrawal.amount)', 'totalAmount')
      .groupBy('withdrawal.status')
      .getRawMany();

    // Xử lý kết quả
    return statusStats.map(stat => ({
      status: stat.status as WithdrawalStatus,
      count: parseInt(stat.count),
      percentage: totalWithdrawals > 0 ? (parseInt(stat.count) / totalWithdrawals) * 100 : 0,
      totalAmount: parseFloat(stat.totalAmount) || 0
    }));
  }

  /**
   * Lấy thống kê xu hướng yêu cầu rút tiền theo thời gian
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @param interval Khoảng thời gian (ngày, tháng, năm)
   * @returns Thống kê xu hướng
   */
  async getTrend(
    begin: number,
    end: number,
    interval: 'day' | 'month' | 'year' = 'month'
  ): Promise<{
    data: {
      label: string;
      timestamp: number;
      count: number;
      amount: number;
    }[];
    totalCount: number;
    totalAmount: number;
  }> {
    // Lấy tất cả yêu cầu rút tiền trong khoảng thời gian
    const withdrawals = await this
      .createQueryBuilder('withdrawal')
      .where('withdrawal.createdAt >= :begin', { begin })
      .andWhere('withdrawal.createdAt <= :end', { end })
      .orderBy('withdrawal.createdAt', 'ASC')
      .getMany();

    // Tính tổng số yêu cầu và tổng số tiền
    const totalCount = withdrawals.length;
    const totalAmount = withdrawals.reduce((sum, withdrawal) => sum + withdrawal.amount, 0);

    // Nhóm dữ liệu theo khoảng thời gian
    const groupedData = new Map<string, { timestamp: number; count: number; amount: number }>();

    for (const withdrawal of withdrawals) {
      const date = new Date(withdrawal.createdAt * 1000);
      let label: string;
      let timestamp: number;

      switch (interval) {
        case 'day':
          label = date.toISOString().split('T')[0]; // YYYY-MM-DD
          timestamp = new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime() / 1000;
          break;
        case 'year':
          label = `${date.getFullYear()}`;
          timestamp = new Date(date.getFullYear(), 0, 1).getTime() / 1000;
          break;
        case 'month':
        default:
          label = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          timestamp = new Date(date.getFullYear(), date.getMonth(), 1).getTime() / 1000;
          break;
      }

      if (groupedData.has(label)) {
        const data = groupedData.get(label)!;
        data.count += 1;
        data.amount += withdrawal.amount;
      } else {
        groupedData.set(label, {
          timestamp,
          count: 1,
          amount: withdrawal.amount
        });
      }
    }

    // Chuyển đổi dữ liệu nhóm thành mảng
    const data = Array.from(groupedData.entries()).map(([label, data]) => ({
      label,
      timestamp: data.timestamp,
      count: data.count,
      amount: data.amount
    }));

    // Sắp xếp theo thời gian
    data.sort((a, b) => a.timestamp - b.timestamp);

    return {
      data,
      totalCount,
      totalAmount
    };
  }
}
