import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateRankRepository } from '@modules/affiliate/repositories/affiliate-rank.repository';
import { AffiliateWithdrawHistoryRepository } from '@modules/affiliate/repositories/affiliate-withdraw-history.repository';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { AffiliateAccountDto } from '../dto';
import { CdnService } from '@/shared/services/cdn.service';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class AffiliateAccountService {
  private readonly logger = new Logger(AffiliateAccountService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateRankRepository: AffiliateRankRepository,
    private readonly affiliateWithdrawHistoryRepository: AffiliateWithdrawHistoryRepository,
    private readonly userRepository: UserRepository,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Lấy thông tin tài khoản affiliate
   * @param userId ID của người dùng
   * @returns Thông tin tài khoản affiliate
   */
  @Transactional()
  async getAccount(userId: number): Promise<AffiliateAccountDto> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Lấy thông tin người dùng
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy thông tin người dùng',
        );
      }

      // Lấy thông tin rank dựa trên giá trị performance
      const rank = await this.affiliateRankRepository.findByPerformance(
        affiliateAccount.performance,
      );
      // Không báo lỗi nếu không tìm thấy rank, để trống thông tin rank

      // Tính tổng số tiền đang xử lý
      const processingAmount = await this.affiliateWithdrawHistoryRepository
        .getProcessingAmountByAffiliateAccountId(affiliateAccount.id);

      // Tạo referral code và link
      const referralCode = this.generateReferralCode(
        user.fullName,
        affiliateAccount.id,
      );
      const referralLink = `https://redai.vn/ref/${referralCode}`;

      return {
        accountInfo: {
          id: affiliateAccount.id,
          partnerName: user.fullName,
          accountType: user.type, // Giả định, cần điều chỉnh theo thực tế
          status: affiliateAccount.status,
          createdAt: affiliateAccount.createdAt,
        },
        rankInfo: rank ? {
          id: rank.id,
          rankName: rank.rankName,
          rankBadge: rank.rankBadge,
          commission: rank.commission,
          minCondition: rank.minCondition,
          maxCondition: rank.maxCondition,
        } : null,
        availableBalance: affiliateAccount.availableBalance,
        processingAmount,
        referralCode,
        referralLink,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate account: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin tài khoản affiliate',
      );
    }
  }

  /**
   * Tạo mã giới thiệu
   * @param fullName Tên đầy đủ của người dùng
   * @param accountId ID tài khoản affiliate
   * @returns Mã giới thiệu
   */
  private generateReferralCode(fullName: string, accountId: number): string {
    // Loại bỏ dấu và khoảng trắng từ tên
    const normalizedName = this.normalizeVietnamese(fullName)
      .replace(/\s+/g, '')
      .toUpperCase();

    // Lấy tối đa 6 ký tự đầu tiên từ tên
    const namePrefix = normalizedName.substring(0, 6);

    // Thêm ID tài khoản vào cuối
    return `${namePrefix}${accountId}`;
  }

  /**
   * Loại bỏ dấu tiếng Việt
   * @param str Chuỗi cần xử lý
   * @returns Chuỗi đã được xử lý
   */
  private normalizeVietnamese(str: string): string {
    return str
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/đ/g, 'd')
      .replace(/Đ/g, 'D');
  }
}
