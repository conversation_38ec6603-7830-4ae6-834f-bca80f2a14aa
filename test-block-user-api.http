### Test API khóa người dùng - Tr<PERSON><PERSON><PERSON> hợp thành công
POST http://localhost:3003/v1/admin/users/1/block
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "reason": "Vi phạm điều khoản sử dụng",
  "info": {
    "details": "Spam nhiều lần",
    "reportedBy": "<EMAIL>"
  }
}

### Test API khóa người dùng đã bị khóa - Sẽ trả về error code 4020
POST http://localhost:3003/v1/admin/users/52/block
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "reason": "User blocked by admin",
  "info": {
    "details": "User has been blocked",
    "reportedBy": "<EMAIL>"
  }
}

### Test API khóa người dùng không tồn tại - Sẽ trả về error code 4000
POST http://localhost:3003/v1/admin/users/99999/block
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "reason": "Test user not found",
  "info": {
    "details": "Testing error handling"
  }
}

### Test API mở khóa người dùng
POST http://localhost:3003/v1/admin/users/52/unblock
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "reason": "Đã xử lý vi phạm",
  "info": {
    "details": "User has been warned",
    "processedBy": "<EMAIL>"
  }
}

### Test API lấy thông tin người dùng
GET http://localhost:3003/v1/admin/users/52
Authorization: Bearer YOUR_JWT_TOKEN_HERE
